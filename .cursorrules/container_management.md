{"name": "Container Management Guidelines", "description": "Rules for managing Docker containers in the TurdParty project to ensure scripts don't disrupt the development environment.", "created_at": "2025-04-05", "version": "1.0.0", "author": "TurdParty Team", "rules": [{"id": "never-stop-other-containers", "title": "Never Stop Other Containers", "description": "Scripts should only manage the specific containers they need and NEVER stop other running containers. This preserves the development environment and prevents disruption of services.", "details": "Each script should only manage (start/stop/remove) the specific containers it needs. Avoid using docker-compose down which stops all containers. Use targeted commands like docker stop <container_name> for specific containers."}, {"id": "preserve-development-environment", "title": "Preserve Running Environments", "description": "The API, DB, UI, and MinIO containers should remain running between test runs.", "details": "Only restart containers if explicitly requested by the user. When in doubt, preserve the state of existing containers."}, {"id": "container-version-management", "title": "Container Version Management", "description": "Keep Docker image versions in sync with installed dependencies.", "details": "Document version requirements in Dockerfiles and compose files. Use version checks to prevent mismatches."}, {"id": "use-isolated-network-config", "title": "Use Isolated Network Configuration", "description": "Test containers should connect to existing service containers.", "details": "Configure proper networking to allow container-to-container communication. Use container names or network aliases for service discovery."}, {"id": "testing-environments", "title": "Testing Environments", "description": "Always use containers for testing rather than trying to run services locally.", "details": "Local execution of services (app.py, npm start) is not reliable; containers provide a consistent environment for testing."}], "examples": [{"title": "Good Pattern: Container-Specific Operations", "code": "docker stop turdparty_playwright 2>/dev/null || true\ndocker rm turdparty_playwright 2>/dev/null || true\ndocker-compose -f docker-compose.yml up -d playwright"}, {"title": "Bad Pattern: Stopping All Containers", "code": "# AVOID THIS - stops all containers\ndocker-compose -f docker-compose.yml down\n\n# AVOID THIS - rebuilds all services\ndocker-compose -f docker-compose.yml up -d --build"}], "file_patterns": [".dockerwrapper/*.sh", "scripts/*.sh"]}