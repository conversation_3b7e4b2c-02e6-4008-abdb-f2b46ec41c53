.ai/docs/0-ai-config/:
total 21
drwxr-xr-x 3 <USER> <GROUP>    5 Mar 22 16:56 claude
drwxr-xr-x 3 <USER> <GROUP>   20 Mar 22 16:55 cursor
-rw-r--r-- 1 <USER> <GROUP> 2353 Mar 22 16:36 SHORTHAND.md
-rw-r--r-- 1 <USER> <GROUP> 4624 Mar 22 16:36 TOKEN_OPTIMIZATION.md
-rw-r--r-- 1 <USER> <GROUP> 7083 Mar 22 16:36 workflow.md
.ai/docs/0-ai-config/claude/:
total 14
drwxr-xr-x 2 <USER> <GROUP>    3 Mar 22 16:56 context
-rw-r--r-- 1 <USER> <GROUP> 7079 Mar 22 16:36 prompting.md
-rw-r--r-- 1 <USER> <GROUP> 3136 Mar 22 16:36 prompt_templates.md
.ai/docs/0-ai-config/claude/context/:
total 30
-rw-r--r-- 1 <USER> <GROUP> 28246 Mar 22 16:56 README.md
.ai/docs/0-ai-config/cursor/:
total 44
-rw-r--r-- 1 <USER> <GROUP> 3796 Mar 22 16:54 001_fastAPI.mdc
-rw-r--r-- 1 <USER> <GROUP> 1227 Mar 22 16:54 002_core_server.mdc
-rw-r--r-- 1 <USER> <GROUP> 1779 Mar 22 16:54 005-PEP8-Style.mdc
-rw-r--r-- 1 <USER> <GROUP> 1396 Mar 22 16:54 006-PEP257-Docstrings.mdc
-rw-r--r-- 1 <USER> <GROUP> 1771 Mar 22 16:54 007-PEP484-Type-Hints.mdc
-rw-r--r-- 1 <USER> <GROUP> 1032 Mar 22 16:54 010-FastAPI-Routes.mdc
-rw-r--r-- 1 <USER> <GROUP> 1005 Mar 22 16:54 020-Flask-Routes.mdc
-rw-r--r-- 1 <USER> <GROUP>  941 Mar 22 16:54 100-MinIO-Integration.mdc
-rw-r--r-- 1 <USER> <GROUP> 1112 Mar 22 16:54 200-Testing-Patterns.mdc
-rw-r--r-- 1 <USER> <GROUP> 1188 Mar 22 16:54 300-Common-Gotchas.mdc
-rw-r--r-- 1 <USER> <GROUP> 1169 Mar 22 16:54 400-Best-Practices.mdc
-rw-r--r-- 1 <USER> <GROUP> 5604 Mar 22 16:36 config.md
-rw-r--r-- 1 <USER> <GROUP> 1043 Mar 22 16:36 .cursorrules
-rw-r--r-- 1 <USER> <GROUP> 1473 Mar 22 16:55 cursorrules.md
-rw-r--r-- 1 <USER> <GROUP> 1355 Mar 22 16:36 README.md
drwxr-xr-x 2 <USER> <GROUP>    7 Mar 22 16:36 rules
-rw-r--r-- 1 <USER> <GROUP> 1459 Mar 22 16:36 rules.index.json
-rw-r--r-- 1 <USER> <GROUP>  486 Mar 22 16:36 settings.json
.ai/docs/0-ai-config/cursor/rules/:
total 15
-rw-r--r-- 1 <USER> <GROUP> 1220 Mar 22 16:36 001_coding_standards.mdc
-rw-r--r-- 1 <USER> <GROUP> 1571 Mar 22 16:36 002_python_standards.mdc
-rw-r--r-- 1 <USER> <GROUP> 2159 Mar 22 16:36 003_javascript_standards.mdc
-rw-r--r-- 1 <USER> <GROUP>  857 Mar 22 16:36 004_nix_shell_usage.mdc
-rw-r--r-- 1 <USER> <GROUP> 1687 Mar 22 16:36 005_devContainers.mdc
.ai/docs/1-context/:
total 4
drwxr-xr-x 2 <USER> <GROUP> 3 Mar 22 16:55 database
drwxr-xr-x 2 <USER> <GROUP> 4 Mar 22 16:55 project
drwxr-xr-x 2 <USER> <GROUP> 4 Mar 22 16:55 standards
drwxr-xr-x 2 <USER> <GROUP> 5 Mar 22 16:55 ui
.ai/docs/1-context/database/:
total 9
-rw-r--r-- 1 <USER> <GROUP> 7098 Mar 22 16:55 database_schema.md
.ai/docs/1-context/project/:
total 9
-rw-r--r-- 1 <USER> <GROUP> 3918 Mar 22 16:55 architecture.md
-rw-r--r-- 1 <USER> <GROUP> 2636 Mar 22 16:36 README.md
.ai/docs/1-context/standards/:
total 11
-rw-r--r-- 1 <USER> <GROUP> 5098 Mar 22 16:36 documentation_standards.md
-rw-r--r-- 1 <USER> <GROUP> 3257 Mar 22 16:55 project_conventions.md
.ai/docs/1-context/ui/:
total 23
-rw-r--r-- 1 <USER> <GROUP> 10805 Mar 22 16:55 ui_components.md
-rw-r--r-- 1 <USER> <GROUP>  3933 Mar 22 16:55 ui_screenshot_status.md
-rw-r--r-- 1 <USER> <GROUP>  5591 Mar 22 16:55 UI_TESTING.md
.ai/docs/2-technical-design/:
total 6
drwxr-xr-x 2 <USER> <GROUP> 3 Mar 22 16:55 api
drwxr-xr-x 2 <USER> <GROUP> 3 Mar 22 16:56 containers
drwxr-xr-x 2 <USER> <GROUP> 3 Mar 22 16:55 development_workflow
drwxr-xr-x 3 <USER> <GROUP> 3 Mar 22 16:36 features
drwxr-xr-x 2 <USER> <GROUP> 3 Mar 22 16:56 workflows
.ai/docs/2-technical-design/api/:
total 12
-rw-r--r-- 1 <USER> <GROUP> 9576 Mar 22 16:55 api_endpoints.md
.ai/docs/2-technical-design/containers/:
total 4
-rw-r--r-- 1 <USER> <GROUP> 1315 Mar 22 16:56 api_container_status.md
.ai/docs/2-technical-design/development_workflow/:
total 10
-rw-r--r-- 1 <USER> <GROUP> 7049 Mar 22 16:55 process_flow.md
.ai/docs/2-technical-design/features/:
total 3
drwxr-xr-x 2 <USER> <GROUP> 3 Mar 22 16:36 _template
.ai/docs/2-technical-design/features/_template/:
total 5
-rw-r--r-- 1 <USER> <GROUP> 2940 Mar 22 16:36 specification.md
.ai/docs/2-technical-design/workflows/:
total 11
-rw-r--r-- 1 <USER> <GROUP> 8129 Mar 22 16:56 workflows.md
.ai/docs/3-development/:
total 14
drwxr-xr-x  2 <USER> <GROUP>    4 Mar 22 16:55 cheatsheets
-rw-r--r--  1 <USER> <GROUP> 3664 Mar 22 16:36 code_hints.md
drwxr-xr-x  2 <USER> <GROUP>    4 Mar 22 16:55 code_index
drwxr-xr-x  2 <USER> <GROUP>    3 Mar 22 16:56 dark_mode
drwxr-xr-x  6 <USER> <GROUP>    6 Mar 22 16:56 lifecycle
drwxr-xr-x  2 <USER> <GROUP>    3 Mar 22 16:56 metadata
drwxr-xr-x  3 <USER> <GROUP>    7 Mar 22 16:55 patterns
drwxr-xr-x  2 <USER> <GROUP>    8 Mar 22 16:56 status_reports
drwxr-xr-x  4 <USER> <GROUP>    5 Mar 22 16:55 support
drwxr-xr-x  2 <USER> <GROUP>    4 Mar 22 16:56 windows_vm
.ai/docs/3-development/cheatsheets/:
total 9
-rw-r--r--  1 <USER> <GROUP> 1534 Mar 22 16:55 combined_server.md
-rw-r--r--  1 <USER> <GROUP> 4560 Mar 22 16:55 file_upload_workflow.md
.ai/docs/3-development/code_index/:
total 8
-rw-r--r--  1 <USER> <GROUP> 1540 Mar 22 16:55 application_init.json
-rw-r--r--  1 <USER> <GROUP> 2424 Mar 22 16:36 README.md
.ai/docs/3-development/dark_mode/:
total 7
-rw-r--r--  1 <USER> <GROUP> 4207 Mar 22 16:56 dark_mode_improvements.md
.ai/docs/3-development/lifecycle/:
total 4
drwxr-xr-x  3 <USER> <GROUP>  3 Mar 22 16:55 daily
drwxr-xr-x  2 <USER> <GROUP>  3 Mar 22 16:55 deltas
drwxr-xr-x  2 <USER> <GROUP>  4 Mar 22 16:55 roadmap
drwxr-xr-x  2 <USER> <GROUP>  3 Mar 22 16:56 todo
.ai/docs/3-development/lifecycle/daily/:
total 13
drwxr-xr-x 2 <USER> <GROUP> 10 Mar 22 16:55 daily_summaries
.ai/docs/3-development/lifecycle/daily/daily_summaries/:
total 44
-rw-r--r-- 1 <USER> <GROUP> 4019 Mar 22 16:55 2025-03-10-ui-screenshot-implementation.md
-rw-r--r-- 1 <USER> <GROUP> 2505 Mar 22 16:55 2025-03-11-dev-environment.md
-rw-r--r-- 1 <USER> <GROUP> 4170 Mar 22 16:55 2025-03-12-expanded-file-upload-test-coverage.md
-rw-r--r-- 1 <USER> <GROUP> 3931 Mar 22 16:55 2025-03-12-performance-monitoring-implementation.md
-rw-r--r-- 1 <USER> <GROUP> 3047 Mar 22 16:55 2025-03-12-service-status-indicators.md
-rw-r--r-- 1 <USER> <GROUP> 3453 Mar 22 16:55 2025-03-12-upload-workflow-tests.md
-rw-r--r-- 1 <USER> <GROUP> 3183 Mar 22 16:55 2025-03-13-vagrant-vm-test-suites.md
-rw-r--r-- 1 <USER> <GROUP> 2840 Mar 22 16:55 2025-03-14-vagrant-vm-management-system.md
.ai/docs/3-development/lifecycle/deltas/:
total 4
-rw-r--r-- 1 <USER> <GROUP> 2430 Mar 22 16:55 CHANGELOG.md
.ai/docs/3-development/lifecycle/roadmap/:
total 9
-rw-r--r-- 1 <USER> <GROUP> 4616 Mar 22 16:55 roadmap.md
-rw-r--r-- 1 <USER> <GROUP> 1441 Mar 22 16:55 ROADMAP.md
.ai/docs/3-development/lifecycle/todo/:
total 8
-rw-r--r-- 1 <USER> <GROUP> 5977 Mar 22 16:56 todo.md
.ai/docs/3-development/metadata/:
total 7
-rw-r--r--  1 <USER> <GROUP> 4170 Mar 22 16:56 server_components.json
.ai/docs/3-development/patterns/:
total 27
-rw-r--r--  1 <USER> <GROUP>  1298 Mar 22 16:55 combined_server.md
drwxr-xr-x  2 <USER> <GROUP>     5 Mar 22 16:36 error_handling
-rw-r--r--  1 <USER> <GROUP>  5981 Mar 22 16:36 error_handling.md
-rw-r--r--  1 <USER> <GROUP> 11915 Mar 22 16:55 file_upload_workflow_pattern.md
-rw-r--r--  1 <USER> <GROUP>  1741 Mar 22 16:36 INDEX.md
.ai/docs/3-development/patterns/error_handling/:
total 8
-rw-r--r-- 1 <USER> <GROUP>  774 Mar 22 16:36 EH-1.context.json
-rw-r--r-- 1 <USER> <GROUP> 1178 Mar 22 16:36 EH-1.md
-rw-r--r-- 1 <USER> <GROUP> 1924 Mar 22 16:36 EH-2.md
.ai/docs/3-development/status_reports/:
total 26
-rw-r--r--  1 <USER> <GROUP> 2650 Mar 22 16:56 2023-03-18.md
-rw-r--r--  1 <USER> <GROUP> 2062 Mar 22 16:56 2025-03-11-system-status.md
-rw-r--r--  1 <USER> <GROUP> 2898 Mar 22 16:56 2025-03-12-vagrant-vm-issues.md
-rw-r--r--  1 <USER> <GROUP> 3473 Mar 22 16:56 2025-03-14-vagrant-vm-management-system.md
-rw-r--r--  1 <USER> <GROUP> 3600 Mar 22 16:56 2025-03-18-file-upload-authentication-fix.md
-rw-r--r--  1 <USER> <GROUP> 3201 Mar 22 16:56 2025-03-21-file-upload-endpoint-fixes.md
.ai/docs/3-development/support/:
total 22
drwxr-xr-x  2 <USER> <GROUP>     6 Mar 22 16:56 debug_history
drwxr-xr-x  2 <USER> <GROUP>     3 Mar 22 16:36 troubleshooting
-rw-r--r--  1 <USER> <GROUP> 17584 Mar 22 16:55 troubleshooting.md
.ai/docs/3-development/support/debug_history/:
total 13
-rw-r--r-- 1 <USER> <GROUP> 1238 Mar 22 16:56 app_initialization.md
-rw-r--r-- 1 <USER> <GROUP> 1971 Mar 22 16:56 frontend_stability.md
-rw-r--r-- 1 <USER> <GROUP> 3031 Mar 22 16:36 README.md
-rw-r--r-- 1 <USER> <GROUP> 2434 Mar 22 16:56 ui_testing_enhancements.md
.ai/docs/3-development/support/troubleshooting/:
total 9
-rw-r--r-- 1 <USER> <GROUP> 7591 Mar 22 16:36 common_issues.md
.ai/docs/3-development/windows_vm/:
total 8
-rw-r--r--  1 <USER> <GROUP> 2499 Mar 22 16:56 windows_vm_state.md
-rw-r--r--  1 <USER> <GROUP> 2264 Mar 22 16:56 windows_vm_todo.md
.ai/docs/4-acceptance/:
total 5
drwxr-xr-x 2 <USER> <GROUP> 3 Mar 22 16:56 future_features
drwxr-xr-x 2 <USER> <GROUP> 3 Mar 22 16:36 performance
drwxr-xr-x 3 <USER> <GROUP> 4 Mar 22 16:36 security
drwxr-xr-x 3 <USER> <GROUP> 3 Mar 22 16:56 testing
.ai/docs/4-acceptance/future_features/:
total 14
-rw-r--r-- 1 <USER> <GROUP> 11548 Mar 22 16:56 future_features.md
.ai/docs/4-acceptance/performance/:
total 13
-rw-r--r-- 1 <USER> <GROUP> 10675 Mar 22 16:36 guidelines.md
.ai/docs/4-acceptance/security/:
total 10
-rw-r--r-- 1 <USER> <GROUP> 7111 Mar 22 16:36 checklist.md
drwxr-xr-x 4 <USER> <GROUP>    4 Mar 22 16:36 tiered
.ai/docs/4-acceptance/security/tiered/:
total 2
drwxr-xr-x 2 <USER> <GROUP> 3 Mar 22 16:36 core
drwxr-xr-x 2 <USER> <GROUP> 3 Mar 22 16:36 implementation
.ai/docs/4-acceptance/security/tiered/core/:
total 4
-rw-r--r-- 1 <USER> <GROUP> 1549 Mar 22 16:36 authentication.md
.ai/docs/4-acceptance/security/tiered/implementation/:
total 5
-rw-r--r-- 1 <USER> <GROUP> 3442 Mar 22 16:36 authentication.md
.ai/docs/4-acceptance/testing/:
total 3
drwxr-xr-x 2 <USER> <GROUP> 3 Mar 22 16:56 test-coverage
.ai/docs/4-acceptance/testing/test-coverage/:
total 5
-rw-r--r-- 1 <USER> <GROUP> 3213 Mar 22 16:56 vagrant-vm-form-submission-coverage.md
