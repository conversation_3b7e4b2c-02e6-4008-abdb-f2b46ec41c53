#!/usr/bin/env python3
"""
This script updates the MinIO configuration in the API container to connect to the host machine.
"""
import os
import logging
import subprocess

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Main function to fix MinIO access in the API container."""
    logger.info("Starting MinIO configuration fix...")
    
    # Check if API container is running
    try:
        api_container = "turdparty_test_api"
        result = subprocess.run(
            ["docker", "ps", "-q", "-f", f"name={api_container}"], 
            capture_output=True, 
            text=True
        )
        
        if not result.stdout.strip():
            logger.error(f"Container {api_container} is not running!")
            return False
        
        logger.info(f"Found API container: {api_container}")
        
        # Create a .env file with MinIO configuration
        env_content = """
# MinIO configuration for direct access to host machine
MINIO_HOST=host.docker.internal
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_DIRECT=true
        """
        
        # Create temporary file
        with open("temp_minio_env", "w") as f:
            f.write(env_content)
        
        # Copy configuration to the container
        logger.info("Copying MinIO configuration to API container...")
        subprocess.run(["docker", "cp", "temp_minio_env", f"{api_container}:/app/.env.minio"])
        
        # Apply the configuration by setting environment variables
        logger.info("Applying MinIO configuration...")
        commands = [
            "echo 'export MINIO_HOST=host.docker.internal' >> ~/.bashrc",
            "echo 'export MINIO_PORT=9000' >> ~/.bashrc",
            "echo 'export MINIO_ACCESS_KEY=minioadmin' >> ~/.bashrc",
            "echo 'export MINIO_SECRET_KEY=minioadmin' >> ~/.bashrc",
            "echo 'export MINIO_DIRECT=true' >> ~/.bashrc",
            "cat /app/.env.minio >> /app/.env",
            "source ~/.bashrc"
        ]
        
        for cmd in commands:
            subprocess.run(["docker", "exec", api_container, "bash", "-c", cmd])
        
        # Clean up
        os.remove("temp_minio_env")
        
        # Test the configuration
        logger.info("Testing MinIO connection...")
        test_cmd = """
        python -c "
        from minio import Minio
        import os
        print('Testing with:', os.environ.get('MINIO_HOST', 'host.docker.internal'))
        client = Minio(
            f\"host.docker.internal:9000\", 
            access_key='minioadmin',
            secret_key='minioadmin',
            secure=False
        )
        buckets = client.list_buckets()
        print(f'Success! Found {len(buckets)} buckets:')
        for bucket in buckets:
            print(f'  - {bucket.name}')
        "
        """
        
        result = subprocess.run(
            ["docker", "exec", api_container, "bash", "-c", test_cmd],
            capture_output=True,
            text=True
        )
        
        if "Success!" in result.stdout:
            logger.info("✅ MinIO configuration successfully applied!")
            logger.info(result.stdout)
            return True
        else:
            logger.error("❌ MinIO connection test failed!")
            logger.error(f"Output: {result.stdout}")
            logger.error(f"Error: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Error fixing MinIO configuration: {e}")
        return False

if __name__ == "__main__":
    main() 