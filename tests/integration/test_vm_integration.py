"""
Integration tests for VM-related tasks.
"""
import os
import pytest
import time
import requests
from unittest.mock import patch

from tasks.vm_tasks import create_vm, start_vm, stop_vm
from db.models.task_status import TaskStatus
from db.models.vagrant_vm import VagrantVM
from db.session import SessionLocal

# Skip tests if not running in Docker
pytestmark = pytest.mark.skipif(
    os.environ.get("IN_DOCKER") != "true",
    reason="Integration tests should only run in Docker"
)

# Constants
API_URL = os.environ.get("API_URL", "http://api:8000")
VAGRANT_API_URL = os.environ.get("VAGRANT_API_URL", "http://host.docker.internal:40000")


@pytest.fixture
def db_session():
    """Create a database session for testing."""
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def mock_vagrant_api():
    """Mock the Vagrant gRPC API."""
    with patch("tasks.vm_tasks.requests.post") as mock_post, \
         patch("tasks.vm_tasks.requests.get") as mock_get:
        # Mock VM creation
        mock_post.return_value.status_code = 200
        mock_post.return_value.json.return_value = {"vm_id": "test-vm-123", "status": "created"}
        
        # Mock VM status check
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = {"status": "running"}
        
        yield mock_post, mock_get


def test_create_vm_task(db_session, mock_vagrant_api):
    """Test the create_vm task."""
    # Create a task status record
    task_id = "test-create-vm"
    task_status = TaskStatus(
        task_id=task_id,
        task_name="tasks.vm_tasks.create_vm",
        status="PENDING"
    )
    db_session.add(task_status)
    db_session.commit()
    
    # Run the task
    result = create_vm.apply_async(
        kwargs={
            "vm_name": "test-vm",
            "vm_type": "ubuntu",
            "memory": 1024,
            "cpus": 1
        },
        task_id=task_id
    )
    
    # Wait for task to complete
    time.sleep(2)
    
    # Refresh task status
    db_session.refresh(task_status)
    
    # Check task status
    assert task_status.status == "SUCCESS"
    
    # Check VM record in database
    vm = db_session.query(VagrantVM).filter(VagrantVM.name == "test-vm").first()
    assert vm is not None
    assert vm.status == "created"
    assert vm.vm_type == "ubuntu"
    assert vm.memory == 1024
    assert vm.cpus == 1


def test_vm_lifecycle_api(mock_vagrant_api):
    """Test the VM lifecycle API endpoints."""
    # Create a VM
    create_data = {
        "name": "test-api-vm",
        "vm_type": "ubuntu",
        "memory": 1024,
        "cpus": 1
    }
    response = requests.post(f"{API_URL}/api/v1/vagrant_vm/", json=create_data)
    assert response.status_code == 200
    result = response.json()
    assert "vm_id" in result
    assert "task_id" in result
    
    vm_id = result["vm_id"]
    
    # Wait for task to complete
    time.sleep(2)
    
    # Start the VM
    response = requests.post(f"{API_URL}/api/v1/vagrant_vm/{vm_id}/start")
    assert response.status_code == 200
    result = response.json()
    assert "task_id" in result
    
    # Wait for task to complete
    time.sleep(2)
    
    # Check VM status
    response = requests.get(f"{API_URL}/api/v1/vagrant_vm/{vm_id}")
    assert response.status_code == 200
    vm_data = response.json()
    assert vm_data["status"] == "running"
    
    # Stop the VM
    response = requests.post(f"{API_URL}/api/v1/vagrant_vm/{vm_id}/stop")
    assert response.status_code == 200
    result = response.json()
    assert "task_id" in result
    
    # Wait for task to complete
    time.sleep(2)
    
    # Check VM status again
    response = requests.get(f"{API_URL}/api/v1/vagrant_vm/{vm_id}")
    assert response.status_code == 200
    vm_data = response.json()
    assert vm_data["status"] == "stopped"
