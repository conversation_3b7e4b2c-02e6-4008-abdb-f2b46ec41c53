"""
Integration tests for the async API.
"""
import pytest
import uuid
import os
import tempfile
import time
import httpx
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from main import app
from db.models.task_status import TaskStatus
from db.models.user import User
from db.models.file_upload import FileUpload
from db.models.vagrant_vm import VagrantVM
from db.types import VMStatus
from db.session import SessionLocal

# Use TestClient for local testing, httpx for Docker testing
client = TestClient(app)

# API URL for Docker testing
API_URL = os.environ.get("API_URL", "http://localhost:8000")

# Flag to determine if we're running in Docker
IN_DOCKER = os.environ.get("IN_DOCKER", "False").lower() == "true"

@pytest.fixture
def db_session():
    """Get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@pytest.fixture
def auth_headers(db_session):
    """Get authentication headers for test user."""
    # Create a test user
    user = User(
        username="testuser",
        email="<EMAIL>",
        full_name="Test User",
        password_hash=User.hash_password("Password123!"),
        roles="user"
    )
    db_session.add(user)
    db_session.commit()

    # Mock the authentication
    with patch("utils.auth.get_current_user", return_value=user):
        yield {"Authorization": "Bearer test_token"}


@pytest.fixture
def mock_celery_task():
    """Mock Celery task."""
    with patch("tasks.file_tasks.upload_file") as mock:
        task = MagicMock()
        task.id = str(uuid.uuid4())
        mock.delay.return_value = task
        yield mock


@pytest.fixture
def test_task_status(db_session, auth_headers):
    """Create a test task status."""
    # Get the user ID from the auth headers
    with patch("utils.auth.get_current_user") as mock:
        user = mock.return_value

        # Create a task status
        task_status = TaskStatus(
            task_id=str(uuid.uuid4()),
            task_type="test_task",
            status="SUCCESS",
            user_id=user.id,
            result={"message": "Task completed successfully"}
        )
        db_session.add(task_status)
        db_session.commit()

        yield task_status


class TestAsyncFileUploadAPI:
    """Tests for async file upload API."""

    def test_async_upload_file(self, auth_headers, mock_celery_task, db_session):
        """Test async file upload endpoint."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b"Test content")
            temp_file_path = temp_file.name

        try:
            # Upload file
            with open(temp_file_path, "rb") as f:
                if IN_DOCKER:
                    # Use httpx for Docker testing
                    with httpx.Client(base_url=API_URL) as client:
                        response = client.post(
                            "/api/v1/async/file_upload",
                            files={"file": ("test.txt", f, "text/plain")},
                            headers=auth_headers
                        )
                else:
                    # Use TestClient for local testing
                    response = client.post(
                        "/api/v1/async/file_upload",
                        files={"file": ("test.txt", f, "text/plain")},
                        headers=auth_headers
                    )

            # Verify response
            assert response.status_code == 202
            response_json = response.json()
            assert "task_id" in response_json
            assert response_json["status"] == "PENDING"
            assert "status_url" in response_json

            # Verify task status was created
            task_id = response_json["task_id"]

            # Wait for task to be processed
            time.sleep(2)

            task_status = db_session.query(TaskStatus).filter(
                TaskStatus.task_id == task_id
            ).first()

            assert task_status is not None
            assert task_status.task_type == "file_upload"

            # If we're running against real services, the task might complete quickly
            if not IN_DOCKER:
                # Verify Celery task was called (only in local testing)
                mock_celery_task.delay.assert_called_once()
        finally:
            # Clean up
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    def test_get_task_status(self, auth_headers, test_task_status, db_session):
        """Test get task status endpoint."""
        # Get task status
        if IN_DOCKER:
            # Use httpx for Docker testing
            with httpx.Client(base_url=API_URL) as client:
                response = client.get(
                    f"/api/v1/async/task/{test_task_status.task_id}",
                    headers=auth_headers
                )
        else:
            # Use TestClient for local testing
            response = client.get(
                f"/api/v1/async/task/{test_task_status.task_id}",
                headers=auth_headers
            )

        # Verify response
        assert response.status_code == 200
        response_json = response.json()
        assert response_json["task_id"] == test_task_status.task_id
        assert response_json["status"] == test_task_status.status
        assert response_json["task_type"] == test_task_status.task_type
        assert response_json["result"] == test_task_status.result

    def test_get_user_tasks(self, auth_headers, db_session):
        """Test get user tasks endpoint."""
        # Get the user ID from the auth headers
        with patch("utils.auth.get_current_user") as mock:
            user = mock.return_value

            # Create multiple task statuses
            for i in range(5):
                task_status = TaskStatus(
                    task_id=str(uuid.uuid4()),
                    task_type=f"test_task_{i}",
                    status="SUCCESS" if i % 2 == 0 else "FAILURE",
                    user_id=user.id,
                    result={"test": i} if i % 2 == 0 else None,
                    error="Test error" if i % 2 == 1 else None
                )
                db_session.add(task_status)
            db_session.commit()

            # Get all tasks
            if IN_DOCKER:
                # Use httpx for Docker testing
                with httpx.Client(base_url=API_URL) as client:
                    response = client.get(
                        "/api/v1/async/tasks",
                        headers=auth_headers
                    )
            else:
                # Use TestClient for local testing
                response = client.get(
                    "/api/v1/async/tasks",
                    headers=auth_headers
                )

            # Verify response
            assert response.status_code == 200
            response_json = response.json()
            assert "items" in response_json
            assert "total" in response_json
            assert response_json["total"] >= 5

            # Filter by status
            if IN_DOCKER:
                # Use httpx for Docker testing
                with httpx.Client(base_url=API_URL) as client:
                    response = client.get(
                        "/api/v1/async/tasks?status=SUCCESS",
                        headers=auth_headers
                    )
            else:
                # Use TestClient for local testing
                response = client.get(
                    "/api/v1/async/tasks?status=SUCCESS",
                    headers=auth_headers
                )

            # Verify filtered response
            assert response.status_code == 200
            response_json = response.json()
            assert all(item["status"] == "SUCCESS" for item in response_json["items"])
