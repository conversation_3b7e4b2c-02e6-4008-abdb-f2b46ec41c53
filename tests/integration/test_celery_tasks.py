"""
Integration tests for Celery tasks.
"""
import pytest
import uuid
import os
import tempfile
import time
import httpx
from fastapi.testclient import TestClient

from main import app
from db.models.task_status import TaskStatus
from db.models.user import User
from db.models.file_upload import FileUpload
from db.session import SessionLocal

# Use TestClient for local testing, httpx for Docker testing
client = TestClient(app)

# API URL for Docker testing
API_URL = os.environ.get("API_URL", "http://localhost:8000")

# Flag to determine if we're running in Docker
IN_DOCKER = os.environ.get("IN_DOCKER", "False").lower() == "true"


@pytest.fixture
def db_session():
    """Get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@pytest.fixture
def test_user(db_session):
    """Create a test user."""
    user = User(
        username="testuser",
        email="<EMAIL>",
        full_name="Test User",
        password_hash=User.hash_password("Password123!"),
        roles="user"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    yield user


@pytest.fixture
def auth_headers(test_user):
    """Get authentication headers for test user."""
    # In a real integration test, we would get a real token
    # For now, we'll use a mock token
    return {"Authorization": f"Bearer test_token_{test_user.id}"}


class TestCeleryTaskExecution:
    """Tests for Celery task execution."""

    def test_file_upload_task(self, auth_headers, test_user, db_session):
        """Test file upload task execution."""
        # Skip this test if not running in Docker
        if not IN_DOCKER:
            pytest.skip("This test requires running in Docker with real services")

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b"Test content for Celery task")
            temp_file_path = temp_file.name

        try:
            # Upload file
            with open(temp_file_path, "rb") as f:
                with httpx.Client(base_url=API_URL) as client:
                    response = client.post(
                        "/api/v1/async/file_upload",
                        files={"file": ("test_celery.txt", f, "text/plain")},
                        headers=auth_headers
                    )

            # Verify response
            assert response.status_code == 202
            response_json = response.json()
            assert "task_id" in response_json
            task_id = response_json["task_id"]

            # Wait for task to complete (up to 10 seconds)
            max_retries = 10
            for i in range(max_retries):
                # Check task status
                task_status = db_session.query(TaskStatus).filter(
                    TaskStatus.task_id == task_id
                ).first()

                if task_status and task_status.status in ["SUCCESS", "FAILURE"]:
                    break

                time.sleep(1)

            # Verify task completed successfully
            assert task_status is not None
            assert task_status.status == "SUCCESS"
            assert task_status.result is not None
            assert "file_id" in task_status.result

            # Verify file was uploaded to database
            file_id = task_status.result["file_id"]
            file_upload = db_session.query(FileUpload).filter(
                FileUpload.id == uuid.UUID(file_id)
            ).first()

            assert file_upload is not None
            assert file_upload.filename == "test_celery.txt"
            assert file_upload.content_type == "text/plain"
            assert file_upload.owner_id == test_user.id

        finally:
            # Clean up
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    def test_task_status_tracking(self, auth_headers, test_user, db_session):
        """Test task status tracking."""
        # Skip this test if not running in Docker
        if not IN_DOCKER:
            pytest.skip("This test requires running in Docker with real services")

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b"Test content for task status tracking")
            temp_file_path = temp_file.name

        try:
            # Upload file
            with open(temp_file_path, "rb") as f:
                with httpx.Client(base_url=API_URL) as client:
                    response = client.post(
                        "/api/v1/async/file_upload",
                        files={"file": ("test_status.txt", f, "text/plain")},
                        headers=auth_headers
                    )

            # Verify response
            assert response.status_code == 202
            response_json = response.json()
            task_id = response_json["task_id"]

            # Get task status immediately
            with httpx.Client(base_url=API_URL) as client:
                response = client.get(
                    f"/api/v1/async/task/{task_id}",
                    headers=auth_headers
                )

            # Verify initial status
            assert response.status_code == 200
            response_json = response.json()
            assert response_json["task_id"] == task_id
            initial_status = response_json["status"]
            assert initial_status in ["PENDING", "STARTED", "SUCCESS"]

            # Wait for task to complete (up to 10 seconds)
            max_retries = 10
            final_status = None
            for i in range(max_retries):
                with httpx.Client(base_url=API_URL) as client:
                    response = client.get(
                        f"/api/v1/async/task/{task_id}",
                        headers=auth_headers
                    )
                
                response_json = response.json()
                if response_json["status"] in ["SUCCESS", "FAILURE"]:
                    final_status = response_json["status"]
                    break

                time.sleep(1)

            # Verify task completed
            assert final_status == "SUCCESS"

            # Get all user tasks
            with httpx.Client(base_url=API_URL) as client:
                response = client.get(
                    "/api/v1/async/tasks",
                    headers=auth_headers
                )

            # Verify user tasks include our task
            assert response.status_code == 200
            response_json = response.json()
            assert "items" in response_json
            assert any(item["task_id"] == task_id for item in response_json["items"])

        finally:
            # Clean up
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
