"""
Integration tests for file selection and VM injection.
"""
import os
import pytest
import time
import requests
from unittest.mock import patch
from io import BytesIO

from db.models.task_status import TaskStatus
from db.models.file_upload import FileUpload
from db.models.file_selection import FileSelection
from db.models.vm_injection import VMInjection
from db.models.vagrant_vm import VagrantVM
from db.session import SessionLocal

# Skip tests if not running in Docker
pytestmark = pytest.mark.skipif(
    os.environ.get("IN_DOCKER") != "true",
    reason="Integration tests should only run in Docker"
)

# Constants
API_URL = os.environ.get("API_URL", "http://api:8000")


@pytest.fixture
def db_session():
    """Create a database session for testing."""
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def mock_vagrant_api():
    """Mock the Vagrant gRPC API."""
    with patch("tasks.vm_tasks.requests.post") as mock_post, \
         patch("tasks.vm_tasks.requests.get") as mock_get:
        # Mock VM creation and operations
        mock_post.return_value.status_code = 200
        mock_post.return_value.json.return_value = {"vm_id": "test-vm-123", "status": "created"}
        
        # Mock VM status check
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = {"status": "running"}
        
        yield mock_post, mock_get


@pytest.fixture
def test_file_upload(db_session):
    """Create a test file upload."""
    # Upload a file through the API
    file_content = b"This is a test file for injection."
    files = {"file": ("test_injection.txt", file_content, "text/plain")}
    data = {"description": "Test file for injection"}
    
    response = requests.post(f"{API_URL}/api/v1/file_upload/", files=files, data=data)
    assert response.status_code == 200
    result = response.json()
    
    # Wait for task to complete
    time.sleep(2)
    
    # Return the file ID
    return result["file_id"]


@pytest.fixture
def test_vm(db_session, mock_vagrant_api):
    """Create a test VM."""
    # Create a VM through the API
    create_data = {
        "name": "test-injection-vm",
        "vm_type": "ubuntu",
        "memory": 1024,
        "cpus": 1
    }
    response = requests.post(f"{API_URL}/api/v1/vagrant_vm/", json=create_data)
    assert response.status_code == 200
    result = response.json()
    
    # Wait for task to complete
    time.sleep(2)
    
    # Start the VM
    vm_id = result["vm_id"]
    response = requests.post(f"{API_URL}/api/v1/vagrant_vm/{vm_id}/start")
    assert response.status_code == 200
    
    # Wait for task to complete
    time.sleep(2)
    
    # Return the VM ID
    return vm_id


def test_file_selection_api(test_file_upload):
    """Test the file selection API endpoint."""
    file_id = test_file_upload
    
    # Create a file selection
    selection_data = {
        "file_id": file_id,
        "target_path": "/tmp/test_file.txt",
        "description": "Test file selection"
    }
    response = requests.post(f"{API_URL}/api/v1/file_selection/", json=selection_data)
    assert response.status_code == 200
    result = response.json()
    assert "selection_id" in result
    
    selection_id = result["selection_id"]
    
    # Get the file selection
    response = requests.get(f"{API_URL}/api/v1/file_selection/{selection_id}")
    assert response.status_code == 200
    selection_data = response.json()
    assert selection_data["file_id"] == file_id
    assert selection_data["target_path"] == "/tmp/test_file.txt"


def test_vm_injection_api(test_file_upload, test_vm):
    """Test the VM injection API endpoint."""
    file_id = test_file_upload
    vm_id = test_vm
    
    # Create a file selection
    selection_data = {
        "file_id": file_id,
        "target_path": "/tmp/injected_file.txt",
        "description": "Test file for VM injection"
    }
    response = requests.post(f"{API_URL}/api/v1/file_selection/", json=selection_data)
    assert response.status_code == 200
    selection_id = response.json()["selection_id"]
    
    # Create a VM injection
    injection_data = {
        "vm_id": vm_id,
        "selection_id": selection_id,
        "description": "Test VM injection"
    }
    
    with patch("tasks.vm_tasks.inject_file_to_vm") as mock_inject:
        mock_inject.return_value = {"status": "success", "message": "File injected successfully"}
        
        response = requests.post(f"{API_URL}/api/v1/vm_injection/", json=injection_data)
        assert response.status_code == 200
        result = response.json()
        assert "injection_id" in result
        assert "task_id" in result
        
        injection_id = result["injection_id"]
        task_id = result["task_id"]
        
        # Wait for task to complete
        time.sleep(2)
        
        # Check task status
        response = requests.get(f"{API_URL}/api/v1/tasks/{task_id}")
        assert response.status_code == 200
        task_result = response.json()
        assert task_result["status"] == "SUCCESS"
        
        # Get the VM injection
        response = requests.get(f"{API_URL}/api/v1/vm_injection/{injection_id}")
        assert response.status_code == 200
        injection_data = response.json()
        assert injection_data["vm_id"] == vm_id
        assert injection_data["selection_id"] == selection_id
