"""
Integration tests for Celery tasks.
"""
import os
import pytest
import time
from unittest.mock import patch
import requests
from io import BytesIO

from tasks.file_tasks import get_content_type, upload_file
# from tasks.monitoring_tasks import check_service_health
from db.models.task_status import TaskStatus
from db.session import SessionLocal

# Skip tests if not running in Docker
pytestmark = pytest.mark.skipif(
    os.environ.get("IN_DOCKER") != "true",
    reason="Integration tests should only run in Docker"
)

# Constants
API_URL = os.environ.get("API_URL", "http://api:8000")
MINIO_ENDPOINT = os.environ.get("MINIO_ENDPOINT", "minio:9000")
MINIO_ACCESS_KEY = os.environ.get("MINIO_ACCESS_KEY", "minioadmin")
MINIO_SECRET_KEY = os.environ.get("MINIO_SECRET_KEY", "minioadmin")


@pytest.fixture
def db_session():
    """Create a database session for testing."""
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


def test_api_health():
    """Test that the API is healthy."""
    response = requests.get(f"{API_URL}/health")
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


def test_get_content_type():
    """Test the get_content_type function."""
    # Test with a text file
    content = b"This is a test file."
    file_path = "test.txt"
    content_type = get_content_type(file_path)
    assert content_type == "text/plain"

    # Test with a PDF file
    pdf_header = b"%PDF-1.5"
    file_path = "test.pdf"
    content_type = get_content_type(file_path)
    assert content_type == "application/pdf"


def _test_check_service_health(db_session):
    """Test the check_service_health task."""
    # Create a task status record
    task_id = "test-health-check"
    task_status = TaskStatus(
        task_id=task_id,
        task_name="tasks.monitoring_tasks.check_service_health",
        status="PENDING"
    )
    db_session.add(task_status)
    db_session.commit()

    # Run the task
    with patch("tasks.monitoring_tasks.requests.get") as mock_get:
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = {"status": "ok"}
        
        result = check_service_health.apply_async(
            args=["http://api:8000/health"],
            task_id=task_id
        )
        
        # Wait for task to complete
        time.sleep(2)
        
        # Refresh task status
        db_session.refresh(task_status)
        
        # Check task status
        assert task_status.status == "SUCCESS"
        assert "healthy" in str(task_status.result)


def test_file_upload_api():
    """Test the file upload API endpoint."""
    # Create a test file
    file_content = b"This is a test file for upload."
    files = {"file": ("test.txt", file_content, "text/plain")}
    data = {"description": "Test file upload"}
    
    # Upload the file
    response = requests.post(f"{API_URL}/api/v1/file_upload/", files=files, data=data)
    
    # Check response
    assert response.status_code == 200
    result = response.json()
    assert "file_id" in result
    assert "task_id" in result
    
    # Wait for task to complete
    time.sleep(2)
    
    # Check task status
    task_id = result["task_id"]
    response = requests.get(f"{API_URL}/api/v1/tasks/{task_id}")
    assert response.status_code == 200
    task_result = response.json()
    assert task_result["status"] == "SUCCESS"
