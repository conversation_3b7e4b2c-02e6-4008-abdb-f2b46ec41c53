"""
Integration tests for the async routes.

This module contains tests for the async routes that use Celery for task processing.
"""
import pytest
import json
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from api.celery_app import celery_app
from api.tasks.file_ops import process_file, analyze_file
from api.tasks.vm_ops import create_vm, inject_file
from main import app


@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    return TestClient(app)


@pytest.fixture
def mock_celery_task():
    """Mock a Celery task result."""
    mock_task = MagicMock()
    mock_task.id = "test-task-id"
    mock_task.status = "PENDING"
    mock_task.date_created = None
    return mock_task


@pytest.fixture
def mock_async_result():
    """Mock an AsyncResult object."""
    mock_result = MagicMock()
    mock_result.status = "SUCCESS"
    mock_result.result = {"message": "Task completed successfully"}
    return mock_result


class TestAsyncRoutes:
    """Test cases for the async routes."""

    def test_submit_generic_task(self, client, mock_celery_task):
        """Test submitting a generic task."""
        # Mock the delay method of the process_file task
        with patch.object(process_file, 'delay', return_value=mock_celery_task):
            response = client.post(
                "/api/v1/async/tasks",
                json={
                    "task_type": "process_file",
                    "task_params": {
                        "file_id": "test-file-id"
                    }
                }
            )
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test-task-id"
            assert data["status"] == "PENDING"

    def test_get_task_status(self, client, mock_async_result):
        """Test getting the status of a task."""
        # Mock the AsyncResult constructor
        with patch('celery.result.AsyncResult', return_value=mock_async_result):
            response = client.get("/api/v1/async/tasks/test-task-id")
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test-task-id"
            assert data["status"] == "SUCCESS"
            assert data["result"] == {"message": "Task completed successfully"}

    def test_cancel_task(self, client):
        """Test cancelling a task."""
        # Mock the control.revoke method
        with patch.object(celery_app.control, 'revoke') as mock_revoke:
            response = client.delete("/api/v1/async/tasks/test-task-id")
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test-task-id"
            assert data["status"] == "REVOKED"
            mock_revoke.assert_called_once_with("test-task-id", terminate=True)

    def test_process_file_endpoint(self, client, mock_celery_task):
        """Test the process file endpoint."""
        # Mock the delay method of the process_file task
        with patch.object(process_file, 'delay', return_value=mock_celery_task):
            response = client.post(
                "/api/v1/async/tasks/file-ops/process",
                json={
                    "file_id": "test-file-id"
                }
            )
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test-task-id"
            assert data["status"] == "PENDING"

    def test_analyze_file_endpoint(self, client, mock_celery_task):
        """Test the analyze file endpoint."""
        # Mock the delay method of the analyze_file task
        with patch.object(analyze_file, 'delay', return_value=mock_celery_task):
            response = client.post(
                "/api/v1/async/tasks/file-ops/analyze",
                json={
                    "file_id": "test-file-id",
                    "analysis_type": "deep"
                }
            )
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test-task-id"
            assert data["status"] == "PENDING"

    def test_create_vm_endpoint(self, client, mock_celery_task):
        """Test the create VM endpoint."""
        # Mock the delay method of the create_vm task
        with patch.object(create_vm, 'delay', return_value=mock_celery_task):
            response = client.post(
                "/api/v1/async/tasks/vm-ops/create",
                json={
                    "vm_config": {
                        "name": "test-vm",
                        "memory": 1024,
                        "cpus": 2
                    }
                }
            )
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test-task-id"
            assert data["status"] == "PENDING"

    def test_inject_file_endpoint(self, client, mock_celery_task):
        """Test the inject file endpoint."""
        # Mock the delay method of the inject_file task
        with patch.object(inject_file, 'delay', return_value=mock_celery_task):
            response = client.post(
                "/api/v1/async/tasks/vm-ops/inject",
                json={
                    "vm_id": "test-vm-id",
                    "file_id": "test-file-id",
                    "injection_path": "/tmp/test-file"
                }
            )
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test-task-id"
            assert data["status"] == "PENDING"

    def test_list_workers_endpoint(self, client):
        """Test the list workers endpoint."""
        # Mock the inspect().active_queues() method
        mock_active_queues = {
            "worker1@host": [
                {"name": "default"},
                {"name": "file_ops"}
            ],
            "worker2@host": [
                {"name": "vm_ops"},
                {"name": "analysis"}
            ]
        }
        with patch.object(celery_app.control, 'inspect') as mock_inspect:
            mock_inspect.return_value.active_queues.return_value = mock_active_queues
            response = client.get("/api/v1/async/workers")
            assert response.status_code == 200
            data = response.json()
            assert data["total"] == 2
            assert len(data["workers"]) == 2
            assert data["workers"][0]["hostname"] == "worker1@host"
            assert "default" in data["workers"][0]["queues"]
            assert "file_ops" in data["workers"][0]["queues"]
            assert data["workers"][1]["hostname"] == "worker2@host"
            assert "vm_ops" in data["workers"][1]["queues"]
            assert "analysis" in data["workers"][1]["queues"]

    def test_get_worker_stats_endpoint(self, client):
        """Test the get worker stats endpoint."""
        # Mock the inspect().active_queues() method
        mock_active_queues = {
            "worker1@host": [
                {"name": "default"},
                {"name": "file_ops"}
            ],
            "worker2@host": [
                {"name": "vm_ops"},
                {"name": "analysis"}
            ]
        }
        with patch.object(celery_app.control, 'inspect') as mock_inspect:
            mock_inspect.return_value.active_queues.return_value = mock_active_queues
            response = client.get("/api/v1/async/workers/stats")
            assert response.status_code == 200
            data = response.json()
            assert data["total_workers"] == 2
            assert data["active_workers"] == 2
            assert "tasks_by_queue" in data
            assert "tasks_by_status" in data
