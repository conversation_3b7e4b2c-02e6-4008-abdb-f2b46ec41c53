"""
Unit tests for the TaskStatus model.
"""
import pytest
import uuid
from datetime import datetime


# Mock TaskStatus class for testing
class MockTaskStatus:
    """Mock TaskStatus class for testing."""

    def __init__(self, task_id, task_type, status, user_id, related_object_type=None, related_object_id=None):
        """Initialize the mock TaskStatus."""
        self.task_id = task_id
        self.task_type = task_type
        self.status = status
        self.user_id = user_id
        self.related_object_type = related_object_type
        self.related_object_id = related_object_id
        self.result = None
        self.error = None

    @property
    def completed(self):
        """Check if the task is completed."""
        return self.status in ["SUCCESS", "FAILURE"]

    @property
    def failed(self):
        """Check if the task failed."""
        return self.status == "FAILURE"

    @property
    def succeeded(self):
        """Check if the task succeeded."""
        return self.status == "SUCCESS"

    @property
    def in_progress(self):
        """Check if the task is in progress."""
        return self.status in ["PENDING", "STARTED", "PROGRESS", "RETRY"]


def test_task_status_properties():
    """Test TaskStatus properties."""
    # Create a task status with PENDING status
    task_status = MockTaskStatus(
        task_id=str(uuid.uuid4()),
        task_type="test_task",
        status="PENDING",
        user_id=uuid.uuid4()
    )

    # Test properties for PENDING status
    assert task_status.completed is False
    assert task_status.failed is False
    assert task_status.succeeded is False
    assert task_status.in_progress is True

    # Test SUCCESS status
    task_status.status = "SUCCESS"
    task_status.result = {"message": "Task completed successfully"}

    assert task_status.completed is True
    assert task_status.failed is False
    assert task_status.succeeded is True
    assert task_status.in_progress is False

    # Test FAILURE status
    task_status.status = "FAILURE"
    task_status.error = "Task failed due to an error"

    assert task_status.completed is True
    assert task_status.failed is True
    assert task_status.succeeded is False
    assert task_status.in_progress is False

    # Test STARTED status
    task_status.status = "STARTED"

    assert task_status.completed is False
    assert task_status.failed is False
    assert task_status.succeeded is False
    assert task_status.in_progress is True

    # Test PROGRESS status
    task_status.status = "PROGRESS"

    assert task_status.completed is False
    assert task_status.failed is False
    assert task_status.succeeded is False
    assert task_status.in_progress is True

    # Test RETRY status
    task_status.status = "RETRY"

    assert task_status.completed is False
    assert task_status.failed is False
    assert task_status.succeeded is False
    assert task_status.in_progress is True


def test_task_status_related_object():
    """Test TaskStatus with related object."""
    # Create a task status with related object
    related_object_id = uuid.uuid4()
    task_status = MockTaskStatus(
        task_id=str(uuid.uuid4()),
        task_type="test_task",
        status="PENDING",
        user_id=uuid.uuid4(),
        related_object_type="test_object",
        related_object_id=related_object_id
    )

    # Verify the task status was created with related object
    assert task_status.related_object_type == "test_object"
    assert task_status.related_object_id == related_object_id
