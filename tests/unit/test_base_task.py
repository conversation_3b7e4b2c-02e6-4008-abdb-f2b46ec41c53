"""
Unit tests for base task classes.
"""
import pytest
import uuid
import traceback
from unittest.mock import patch, MagicMock, call

from tasks.base_task import TurdPartyTask, VMTask
from db.models.task_status import TaskStatus


class TestTurdPartyTask:
    """Tests for TurdPartyTask base class."""

    @pytest.fixture
    def mock_session(self):
        """Mock database session."""
        with patch("db.session.SessionLocal") as mock_session_local:
            session = MagicMock()
            mock_session_local.return_value = session
            yield session

    @pytest.fixture
    def mock_task(self):
        """Create a mock task instance."""
        task = TurdPartyTask()
        task.name = "test_task"
        return task

    def test_on_success(self, mock_session, mock_task):
        """Test on_success method."""
        # Set up mocks
        task_id = str(uuid.uuid4())
        retval = {"result": "success"}
        args = ("arg1", "arg2")
        kwargs = {"kwarg1": "value1"}

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.return_value.filter.return_value.first.return_value = task_status

        # Mock logger
        with patch("tasks.base_task.logger") as mock_logger:
            # Call on_success
            mock_task.on_success(retval, task_id, args, kwargs)

            # Verify logger was called
            mock_logger.info.assert_called_once()

            # Verify task status was updated
            assert task_status.status == "SUCCESS"
            assert task_status.result == retval

            # Verify session was closed
            mock_session.close.assert_called_once()

    def test_on_success_with_non_dict_result(self, mock_session, mock_task):
        """Test on_success method with non-dict result."""
        # Set up mocks
        task_id = str(uuid.uuid4())
        retval = "success"  # String, not dict
        args = ("arg1", "arg2")
        kwargs = {"kwarg1": "value1"}

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.return_value.filter.return_value.first.return_value = task_status

        # Mock logger
        with patch("tasks.base_task.logger") as mock_logger:
            # Call on_success
            mock_task.on_success(retval, task_id, args, kwargs)

            # Verify logger was called
            mock_logger.info.assert_called_once()

            # Verify task status was updated
            assert task_status.status == "SUCCESS"
            assert task_status.result == {"result": "success"}

    def test_on_retry(self, mock_session, mock_task):
        """Test on_retry method."""
        # Set up mocks
        task_id = str(uuid.uuid4())
        exc = Exception("Test exception")
        args = ("arg1", "arg2")
        kwargs = {"kwarg1": "value1"}
        einfo = MagicMock()

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.return_value.filter.return_value.first.return_value = task_status

        # Mock logger and traceback
        with patch("tasks.base_task.logger") as mock_logger, \
             patch("tasks.base_task.traceback.format_exc", return_value="Traceback"):
            # Call on_retry
            mock_task.on_retry(exc, task_id, args, kwargs, einfo)

            # Verify logger was called
            mock_logger.warning.assert_called_once()

            # Verify task status was updated
            assert task_status.status == "RETRY"
            assert "Test exception" in task_status.error

            # Verify session was closed
            mock_session.close.assert_called_once()

    def test_on_failure(self, mock_session, mock_task):
        """Test on_failure method."""
        # Set up mocks
        task_id = str(uuid.uuid4())
        exc = Exception("Test exception")
        args = ("arg1", "arg2")
        kwargs = {"kwarg1": "value1"}
        einfo = MagicMock()

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.return_value.filter.return_value.first.return_value = task_status

        # Mock logger and traceback
        with patch("tasks.base_task.logger") as mock_logger, \
             patch("tasks.base_task.traceback.format_exc", return_value="Traceback"):
            # Call on_failure
            mock_task.on_failure(exc, task_id, args, kwargs, einfo)

            # Verify logger was called
            mock_logger.error.assert_called_once()

            # Verify task status was updated
            assert task_status.status == "FAILURE"
            assert "Test exception" in task_status.error

            # Verify session was closed
            mock_session.close.assert_called_once()

    def test_on_success_no_task_status(self, mock_session, mock_task):
        """Test on_success method when task status is not found."""
        # Set up mocks
        task_id = str(uuid.uuid4())
        retval = {"result": "success"}
        args = ("arg1", "arg2")
        kwargs = {"kwarg1": "value1"}

        # Mock task status not found
        mock_session.query.return_value.filter.return_value.first.return_value = None

        # Mock logger
        with patch("tasks.base_task.logger") as mock_logger:
            # Call on_success
            mock_task.on_success(retval, task_id, args, kwargs)

            # Verify logger was called
            mock_logger.info.assert_called_once()

            # Verify session was closed
            mock_session.close.assert_called_once()

    def test_on_success_exception(self, mock_session, mock_task):
        """Test on_success method when an exception occurs."""
        # Set up mocks
        task_id = str(uuid.uuid4())
        retval = {"result": "success"}
        args = ("arg1", "arg2")
        kwargs = {"kwarg1": "value1"}

        # Mock session to raise exception
        mock_session.query.side_effect = Exception("Database error")

        # Mock logger
        with patch("tasks.base_task.logger") as mock_logger:
            # Call on_success
            mock_task.on_success(retval, task_id, args, kwargs)

            # Verify error was logged
            mock_logger.error.assert_called_once()

            # No other assertions needed, just verify it doesn't raise an exception


class TestVMTask(TestTurdPartyTask):
    """Tests for VMTask base class."""

    @pytest.fixture
    def mock_task(self):
        """Create a mock task instance."""
        task = VMTask()
        task.name = "test_vm_task"
        return task

    def test_on_failure(self, mock_session, mock_task):
        """Test on_failure method."""
        # Set up mocks
        task_id = str(uuid.uuid4())
        exc = Exception("Test exception")
        args = ("arg1", "arg2")
        kwargs = {"kwarg1": "value1"}
        einfo = MagicMock()

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.return_value.filter.return_value.first.return_value = task_status

        # Mock parent class on_failure and logger
        with patch.object(TurdPartyTask, "on_failure") as mock_parent_on_failure, \
             patch("tasks.base_task.logger") as mock_logger:
            # Call on_failure
            mock_task.on_failure(exc, task_id, args, kwargs, einfo)

            # Verify parent on_failure was called
            mock_parent_on_failure.assert_called_once_with(exc, task_id, args, kwargs, einfo)

            # Verify logger was called
            mock_logger.info.assert_called_once()
