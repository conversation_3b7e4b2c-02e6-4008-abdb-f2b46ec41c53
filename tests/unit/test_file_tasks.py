"""
Unit tests for file-related Celery tasks.
"""
import pytest
import uuid
import os
import tempfile
from unittest.mock import patch, MagicMock, call

from tasks.file_tasks import upload_file, create_file_selection, get_content_type
from db.models.task_status import TaskStatus
from db.models.file_upload import FileUpload
from db.models.file_selection import FileSelection


def test_get_content_type():
    """Test get_content_type function."""
    # Test with common file types
    assert get_content_type("test.txt") == "text/plain"
    assert get_content_type("test.pdf") == "application/pdf"
    assert get_content_type("test.jpg") == "image/jpeg"
    assert get_content_type("test.png") == "image/png"

    # Test with unknown file type
    assert get_content_type("test.unknown") == "application/octet-stream"


class TestUploadFileTask:
    """Tests for upload_file task."""

    @pytest.fixture
    def mock_session(self):
        """Mock database session."""
        with patch("tasks.file_tasks.SessionLocal") as mock_session_local:
            session = MagicMock()
            mock_session_local.return_value = session
            yield session

    @pytest.fixture
    def mock_minio_client(self):
        """Mock MinIO client."""
        with patch("services.minio_client.MinioClient") as mock_client_class:
            client = MagicMock()
            mock_client_class.return_value = client
            yield client

    @pytest.fixture
    def mock_celery_request(self):
        """Mock Celery request context."""
        task_id = str(uuid.uuid4())
        with patch("tasks.file_tasks.upload_file.request.id", task_id):
            yield task_id

    def test_upload_file_success(self, mock_session, mock_minio_client, mock_celery_request):
        """Test successful file upload."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b"Test content")
            temp_file_path = temp_file.name

        try:
            # Set up mocks
            user_id = str(uuid.uuid4())
            file_upload = MagicMock(spec=FileUpload)
            file_upload.id = uuid.uuid4()
            file_upload.filename = os.path.basename(temp_file_path)
            file_upload.content_type = "text/plain"
            file_upload.size = os.path.getsize(temp_file_path)

            mock_session.add.return_value = None
            mock_session.commit.return_value = None
            mock_session.refresh.return_value = None

            # Mock task status
            task_status = MagicMock(spec=TaskStatus)
            mock_session.query.return_value.filter.return_value.first.return_value = task_status

            # Execute task
            result = upload_file(temp_file_path, user_id)

            # Verify MinIO client was called
            mock_minio_client.upload_file.assert_called_once()
            bucket_name, object_name, file_path = mock_minio_client.upload_file.call_args[1].values()
            assert bucket_name == "uploads"
            assert file_path == temp_file_path

            # Verify file upload was created
            mock_session.add.assert_called_once()
            added_file_upload = mock_session.add.call_args[0][0]
            assert isinstance(added_file_upload, FileUpload)
            assert added_file_upload.filename == os.path.basename(temp_file_path)
            assert added_file_upload.content_type == "text/plain"
            assert added_file_upload.size == os.path.getsize(temp_file_path)
            assert added_file_upload.owner_id == uuid.UUID(user_id)

            # Verify task status was updated
            assert task_status.status == "SUCCESS"
            assert task_status.related_object_id is not None
            assert task_status.result is not None

            # Verify result
            assert "file_id" in result
            assert "filename" in result
            assert "content_type" in result
            assert "size" in result
        finally:
            # Clean up
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    def test_upload_file_failure(self, mock_session, mock_minio_client, mock_celery_request):
        """Test file upload failure."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b"Test content")
            temp_file_path = temp_file.name

        try:
            # Set up mocks
            user_id = str(uuid.uuid4())

            # Mock MinIO client to raise exception
            mock_minio_client.upload_file.side_effect = Exception("Upload failed")

            # Mock task status
            task_status = MagicMock(spec=TaskStatus)
            mock_session.query.return_value.filter.return_value.first.return_value = task_status

            # Mock retry method
            upload_file.retry = MagicMock(side_effect=Exception("Retry"))

            # Execute task and expect exception
            with pytest.raises(Exception, match="Retry"):
                upload_file(temp_file_path, user_id)

            # Verify task status was updated
            assert task_status.status == "FAILURE"
            assert task_status.error is not None

            # Verify retry was called
            upload_file.retry.assert_called_once()
        finally:
            # Clean up
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)


class TestCreateFileSelectionTask:
    """Tests for create_file_selection task."""

    @pytest.fixture
    def mock_session(self):
        """Mock database session."""
        with patch("tasks.file_tasks.SessionLocal") as mock_session_local:
            session = MagicMock()
            mock_session_local.return_value = session
            yield session

    @pytest.fixture
    def mock_celery_request(self):
        """Mock Celery request context."""
        task_id = str(uuid.uuid4())
        with patch("tasks.file_tasks.create_file_selection.request.id", task_id):
            yield task_id

    def test_create_file_selection_success(self, mock_session, mock_celery_request):
        """Test successful file selection creation."""
        # Set up mocks
        file_upload_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())

        # Mock file upload
        file_upload = MagicMock(spec=FileUpload)
        file_upload.id = uuid.UUID(file_upload_id)
        file_upload.filename = "test.txt"
        mock_session.query.return_value.filter.return_value.first.return_value = file_upload

        # Mock file selection
        file_selection = MagicMock(spec=FileSelection)
        file_selection.id = uuid.uuid4()
        file_selection.name = f"Selection of {file_upload.filename}"

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.return_value.filter.return_value.first.return_value = task_status

        # Execute task
        result = create_file_selection(file_upload_id, user_id)

        # Verify file selection was created
        mock_session.add.assert_called_once()
        added_file_selection = mock_session.add.call_args[0][0]
        assert isinstance(added_file_selection, FileSelection)
        assert added_file_selection.file_upload_id == file_upload.id
        assert added_file_selection.owner_id == uuid.UUID(user_id)

        # Verify task status was updated
        assert task_status.status == "SUCCESS"
        assert task_status.related_object_id is not None
        assert task_status.result is not None

        # Verify result
        assert "file_selection_id" in result
        assert "file_upload_id" in result
        assert "name" in result

    def test_create_file_selection_not_found(self, mock_session, mock_celery_request):
        """Test file selection creation with non-existent file upload."""
        # Set up mocks
        file_upload_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())

        # Mock file upload not found
        mock_session.query.return_value.filter.return_value.first.return_value = None

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=None)))),
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=task_status))))
        ]

        # Mock retry method
        create_file_selection.retry = MagicMock(side_effect=Exception("Retry"))

        # Execute task and expect exception
        with pytest.raises(Exception, match="Retry"):
            create_file_selection(file_upload_id, user_id)

        # Verify task status was updated
        assert task_status.status == "FAILURE"
        assert task_status.error is not None

        # Verify retry was called
        create_file_selection.retry.assert_called_once()
