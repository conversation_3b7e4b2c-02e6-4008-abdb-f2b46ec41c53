"""
Unit tests for monitoring-related Celery tasks.
"""
import pytest
import uuid
from unittest.mock import patch, MagicMock, call
from datetime import datetime

from tasks.monitoring_tasks import check_injection_status, retry_injection, cleanup_vm
from db.models.task_status import TaskStatus
from db.models.vagrant_vm import VagrantVM
from db.models.vm_injection import VMInjection
from db.types import VMStatus


class TestCheckInjectionStatusTask:
    """Tests for check_injection_status task."""

    @pytest.fixture
    def mock_session(self):
        """Mock database session."""
        with patch("tasks.monitoring_tasks.SessionLocal") as mock_session_local:
            session = MagicMock()
            mock_session_local.return_value = session
            yield session

    @pytest.fixture
    def mock_injection_service(self):
        """Mock injection service."""
        with patch("services.injection_service.InjectionService") as mock_service_class:
            service = MagicMock()
            mock_service_class.return_value = service
            yield service

    @pytest.fixture
    def mock_celery_request(self):
        """Mock Celery request context."""
        task_id = str(uuid.uuid4())
        with patch("tasks.monitoring_tasks.check_injection_status.request.id", task_id):
            yield task_id

    def test_check_injection_status_completed(self, mock_session, mock_injection_service, mock_celery_request):
        """Test checking status of completed injection."""
        # Set up mocks
        injection_id = str(uuid.uuid4())

        # Mock injection
        injection = MagicMock(spec=VMInjection)
        injection.id = uuid.UUID(injection_id)
        injection.status = "completed"
        mock_session.query.return_value.filter.return_value.first.return_value = injection

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=injection)))),
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=task_status))))
        ]

        # Execute task
        result = check_injection_status(injection_id)

        # Verify injection service was not called
        mock_injection_service.check_injection_progress.assert_not_called()

        # Verify task status was updated
        assert task_status.status == "SUCCESS"
        assert task_status.result is not None

        # Verify result
        assert "injection_id" in result
        assert "status" in result
        assert result["status"] == "completed"
        assert "progress" in result
        assert result["progress"] is None

    def test_check_injection_status_processing(self, mock_session, mock_injection_service, mock_celery_request):
        """Test checking status of processing injection."""
        # Set up mocks
        injection_id = str(uuid.uuid4())

        # Mock injection
        injection = MagicMock(spec=VMInjection)
        injection.id = uuid.UUID(injection_id)
        injection.status = "processing"

        # Mock progress
        progress = {
            "percent_complete": 50,
            "current_step": "Copying file"
        }
        mock_injection_service.check_injection_progress.return_value = progress

        # Mock query results
        mock_session.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=injection)))),
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=MagicMock(spec=TaskStatus)))))
        ]

        # Execute task
        result = check_injection_status(injection_id)

        # Verify injection service was called
        mock_injection_service.check_injection_progress.assert_called_once_with(injection)

        # Verify result
        assert "injection_id" in result
        assert "status" in result
        assert result["status"] == "processing"
        assert "progress" in result
        assert result["progress"] == progress

    def test_check_injection_status_not_found(self, mock_session, mock_celery_request):
        """Test checking status of non-existent injection."""
        # Set up mocks
        injection_id = str(uuid.uuid4())

        # Mock injection not found
        mock_session.query.return_value.filter.return_value.first.return_value = None

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=None)))),
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=task_status))))
        ]

        # Execute task and expect exception
        with pytest.raises(ValueError, match=f"VM injection with id {injection_id} not found"):
            check_injection_status(injection_id)

        # Verify task status was updated
        assert task_status.status == "FAILURE"
        assert task_status.error is not None


class TestRetryInjectionTask:
    """Tests for retry_injection task."""

    @pytest.fixture
    def mock_session(self):
        """Mock database session."""
        with patch("tasks.monitoring_tasks.SessionLocal") as mock_session_local:
            session = MagicMock()
            mock_session_local.return_value = session
            yield session

    @pytest.fixture
    def mock_create_vm_injection(self):
        """Mock create_vm_injection task."""
        with patch("tasks.monitoring_tasks.create_vm_injection") as mock_task:
            task = MagicMock()
            task_instance = MagicMock()
            task_instance.id = str(uuid.uuid4())
            task.delay.return_value = task_instance
            yield task

    @pytest.fixture
    def mock_celery_request(self):
        """Mock Celery request context."""
        task_id = str(uuid.uuid4())
        with patch("tasks.monitoring_tasks.retry_injection.request.id", task_id):
            yield task_id

    def test_retry_injection_success(self, mock_session, mock_create_vm_injection, mock_celery_request):
        """Test successful injection retry."""
        # Set up mocks
        injection_id = str(uuid.uuid4())

        # Mock injection
        injection = MagicMock(spec=VMInjection)
        injection.id = uuid.UUID(injection_id)
        injection.status = "failed"
        injection.vagrant_vm_id = uuid.uuid4()
        injection.file_selection_id = uuid.uuid4()
        injection.owner_id = uuid.uuid4()

        # Mock query results
        mock_session.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=injection)))),
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=MagicMock(spec=TaskStatus)))))
        ]

        # Execute task
        result = retry_injection(injection_id)

        # Verify injection status was updated
        assert injection.status == "retrying"

        # Verify create_vm_injection task was called
        mock_create_vm_injection.delay.assert_called_once_with(
            str(injection.vagrant_vm_id),
            str(injection.file_selection_id),
            str(injection.owner_id)
        )

        # Verify result
        assert "injection_id" in result
        assert "new_task_id" in result

    def test_retry_injection_not_failed(self, mock_session, mock_celery_request):
        """Test retrying an injection that is not failed."""
        # Set up mocks
        injection_id = str(uuid.uuid4())

        # Mock injection
        injection = MagicMock(spec=VMInjection)
        injection.id = uuid.UUID(injection_id)
        injection.status = "completed"  # Not failed

        # Mock query results
        mock_session.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=injection)))),
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=MagicMock(spec=TaskStatus)))))
        ]

        # Execute task and expect exception
        with pytest.raises(ValueError, match=f"VM injection with id {injection_id} is not failed"):
            retry_injection(injection_id)


class TestCleanupVMTask:
    """Tests for cleanup_vm task."""

    @pytest.fixture
    def mock_session(self):
        """Mock database session."""
        with patch("tasks.monitoring_tasks.SessionLocal") as mock_session_local:
            session = MagicMock()
            mock_session_local.return_value = session
            yield session

    @pytest.fixture
    def mock_vagrant_client(self):
        """Mock Vagrant client."""
        with patch("services.vagrant_client.VagrantClient") as mock_client_class:
            client = MagicMock()
            mock_client_class.return_value = client
            yield client

    @pytest.fixture
    def mock_celery_request(self):
        """Mock Celery request context."""
        task_id = str(uuid.uuid4())
        with patch("tasks.monitoring_tasks.cleanup_vm.request.id", task_id):
            yield task_id

    def test_cleanup_vm_success(self, mock_session, mock_vagrant_client, mock_celery_request):
        """Test successful VM cleanup."""
        # Set up mocks
        vm_id = str(uuid.uuid4())

        # Mock VM
        vm = MagicMock(spec=VagrantVM)
        vm.id = uuid.UUID(vm_id)
        vm.status = VMStatus.RUNNING.value

        # Mock query results
        mock_session.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=vm)))),
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=MagicMock(spec=TaskStatus)))))
        ]

        # Execute task
        result = cleanup_vm(vm_id)

        # Verify Vagrant client was called
        mock_vagrant_client.destroy_vm.assert_called_once_with(str(vm.id))

        # Verify VM status was updated
        assert vm.status == VMStatus.DESTROYED.value
        assert vm.last_action == "destroy"

        # Verify result
        assert "vm_id" in result
        assert "status" in result
        assert result["status"] == VMStatus.DESTROYED.value

    def test_cleanup_vm_not_found(self, mock_session, mock_celery_request):
        """Test cleanup of non-existent VM."""
        # Set up mocks
        vm_id = str(uuid.uuid4())

        # Mock VM not found
        mock_session.query.return_value.filter.return_value.first.return_value = None

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=None)))),
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=task_status))))
        ]

        # Execute task and expect exception
        with pytest.raises(ValueError, match=f"VM with id {vm_id} not found"):
            cleanup_vm(vm_id)

        # Verify task status was updated
        assert task_status.status == "FAILURE"
        assert task_status.error is not None
