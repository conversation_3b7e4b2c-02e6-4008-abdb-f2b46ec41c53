"""
Unit tests for VM-related Celery tasks.
"""
import pytest
import uuid
from unittest.mock import patch, MagicMock, call
from datetime import datetime

from tasks.vm_tasks import provision_vm, create_vm_injection
from db.models.task_status import TaskStatus
from db.models.vagrant_vm import VagrantVM
from db.models.vm_injection import VMInjection
from db.models.file_selection import FileSelection
from db.types import VMStatus


class TestProvisionVMTask:
    """Tests for provision_vm task."""

    @pytest.fixture
    def mock_session(self):
        """Mock database session."""
        with patch("tasks.vm_tasks.SessionLocal") as mock_session_local:
            session = MagicMock()
            mock_session_local.return_value = session
            yield session

    @pytest.fixture
    def mock_vagrant_client(self):
        """Mock Vagrant client."""
        with patch("services.vagrant_client.VagrantClient") as mock_client_class:
            client = MagicMock()
            mock_client_class.return_value = client
            yield client

    @pytest.fixture
    def mock_celery_request(self):
        """Mock Celery request context."""
        task_id = str(uuid.uuid4())
        with patch("tasks.vm_tasks.provision_vm.request.id", task_id):
            yield task_id

    def test_provision_vm_success(self, mock_session, mock_vagrant_client, mock_celery_request):
        """Test successful VM provisioning."""
        # Set up mocks
        vm_config = {
            "name": "test-vm",
            "description": "Test VM",
            "template": "ubuntu/focal64",
            "memory_mb": 1024,
            "cpus": 1,
            "disk_gb": 20,
            "domain": "TurdParty"
        }
        user_id = str(uuid.uuid4())

        # Mock VM
        vm = MagicMock(spec=VagrantVM)
        vm.id = uuid.uuid4()
        vm.name = vm_config["name"]
        vm.template = vm_config["template"]
        vm.memory_mb = vm_config["memory_mb"]
        vm.cpus = vm_config["cpus"]
        vm.disk_gb = vm_config["disk_gb"]

        # Mock Vagrant client response
        vagrant_result = {
            "vagrant_id": "test-vagrant-id",
            "ip_address": "*************",
            "ssh_port": 2222
        }
        mock_vagrant_client.create_vm.return_value = vagrant_result

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.return_value.filter.return_value.first.return_value = task_status

        # Execute task
        result = provision_vm(vm_config, user_id)

        # Verify VM was created
        mock_session.add.assert_called_once()
        added_vm = mock_session.add.call_args[0][0]
        assert isinstance(added_vm, VagrantVM)
        assert added_vm.name == vm_config["name"]
        assert added_vm.template == vm_config["template"]
        assert added_vm.memory_mb == vm_config["memory_mb"]
        assert added_vm.cpus == vm_config["cpus"]
        assert added_vm.disk_gb == vm_config["disk_gb"]
        assert added_vm.owner_id == uuid.UUID(user_id)

        # Verify Vagrant client was called
        mock_vagrant_client.create_vm.assert_called_once()

        # Verify VM status was updated
        assert added_vm.status == VMStatus.RUNNING.value
        assert added_vm.vagrant_id == vagrant_result["vagrant_id"]
        assert added_vm.ip_address == vagrant_result["ip_address"]
        assert added_vm.ssh_port == vagrant_result["ssh_port"]

        # Verify task status was updated
        assert task_status.status == "SUCCESS"
        assert task_status.result is not None

        # Verify result
        assert "vm_id" in result
        assert "name" in result
        assert "status" in result
        assert "ip_address" in result
        assert "ssh_port" in result

    def test_provision_vm_failure(self, mock_session, mock_vagrant_client, mock_celery_request):
        """Test VM provisioning failure."""
        # Set up mocks
        vm_config = {
            "name": "test-vm",
            "description": "Test VM",
            "template": "ubuntu/focal64",
            "memory_mb": 1024,
            "cpus": 1,
            "disk_gb": 20,
            "domain": "TurdParty"
        }
        user_id = str(uuid.uuid4())

        # Mock Vagrant client to raise exception
        mock_vagrant_client.create_vm.side_effect = Exception("Provisioning failed")

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.return_value.filter.return_value.first.return_value = task_status

        # Mock retry method
        provision_vm.retry = MagicMock(side_effect=Exception("Retry"))

        # Execute task and expect exception
        with pytest.raises(Exception, match="Retry"):
            provision_vm(vm_config, user_id)

        # Verify VM status was updated to ERROR
        added_vm = mock_session.add.call_args[0][0]
        assert added_vm.status == VMStatus.ERROR.value
        assert added_vm.error_message is not None

        # Verify task status was updated
        assert task_status.status == "FAILURE"
        assert task_status.error is not None

        # Verify retry was called
        provision_vm.retry.assert_called_once()


class TestCreateVMInjectionTask:
    """Tests for create_vm_injection task."""

    @pytest.fixture
    def mock_session(self):
        """Mock database session."""
        with patch("tasks.vm_tasks.SessionLocal") as mock_session_local:
            session = MagicMock()
            mock_session_local.return_value = session
            yield session

    @pytest.fixture
    def mock_injection_service(self):
        """Mock injection service."""
        with patch("tasks.vm_tasks.InjectionService") as mock_service_class:
            service = MagicMock()
            mock_service_class.return_value = service
            yield service

    @pytest.fixture
    def mock_celery_request(self):
        """Mock Celery request context."""
        task_id = str(uuid.uuid4())
        with patch("tasks.vm_tasks.create_vm_injection.request.id", task_id):
            yield task_id

    def test_create_vm_injection_success(self, mock_session, mock_injection_service, mock_celery_request):
        """Test successful VM injection creation."""
        # Set up mocks
        vm_id = str(uuid.uuid4())
        file_selection_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())

        # Mock VM
        vm = MagicMock(spec=VagrantVM)
        vm.id = uuid.UUID(vm_id)
        vm.name = "test-vm"
        vm.status = VMStatus.RUNNING.value

        # Mock file selection
        file_selection = MagicMock(spec=FileSelection)
        file_selection.id = uuid.UUID(file_selection_id)
        file_selection.name = "test-selection"

        # Mock query results
        mock_session.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=vm)))),
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=file_selection)))),
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=MagicMock(spec=TaskStatus)))))
        ]

        # Mock injection service response
        injection_result = {
            "target_path": "/tmp/injected-file"
        }
        mock_injection_service.inject_file.return_value = injection_result

        # Execute task
        result = create_vm_injection(vm_id, file_selection_id, user_id)

        # Verify injection was created
        mock_session.add.assert_called_once()
        added_injection = mock_session.add.call_args[0][0]
        assert isinstance(added_injection, VMInjection)
        assert added_injection.vagrant_vm_id == vm.id
        assert added_injection.file_selection_id == file_selection.id
        assert added_injection.owner_id == uuid.UUID(user_id)

        # Verify injection service was called
        mock_injection_service.inject_file.assert_called_once()

        # Verify injection status was updated
        assert added_injection.status == "completed"

        # Verify result
        assert "injection_id" in result
        assert "vm_id" in result
        assert "file_selection_id" in result
        assert "status" in result
        assert "target_path" in result

    def test_create_vm_injection_vm_not_found(self, mock_session, mock_injection_service, mock_celery_request):
        """Test VM injection creation with non-existent VM."""
        # Set up mocks
        vm_id = str(uuid.uuid4())
        file_selection_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())

        # Mock VM not found
        mock_session.query.return_value.filter.return_value.first.return_value = None

        # Mock task status
        task_status = MagicMock(spec=TaskStatus)
        mock_session.query.side_effect = [
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=None)))),
            MagicMock(filter=MagicMock(return_value=MagicMock(first=MagicMock(return_value=task_status))))
        ]

        # Mock retry method
        create_vm_injection.retry = MagicMock(side_effect=Exception("Retry"))

        # Execute task and expect exception
        with pytest.raises(Exception, match="Retry"):
            create_vm_injection(vm_id, file_selection_id, user_id)

        # Verify task status was updated
        assert task_status.status == "FAILURE"
        assert task_status.error is not None

        # Verify retry was called
        create_vm_injection.retry.assert_called_once()
