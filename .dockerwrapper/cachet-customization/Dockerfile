# Dockerfile for customized Cachet with dark theme and service icons
FROM cachethq/docker:latest

# Install additional tools for customization
RUN apt-get update && apt-get install -y \
    curl \
    jq \
    && rm -rf /var/lib/apt/lists/*

# Copy custom theme files
COPY dark-theme.css /var/www/html/public/css/dark-theme.css
COPY service-icons.js /var/www/html/public/js/service-icons.js

# Copy customization scripts
COPY customize-layout.sh /usr/local/bin/customize-layout.sh
COPY init-cachet-customization.sh /usr/local/bin/init-cachet-customization.sh

# Make scripts executable
RUN chmod +x /usr/local/bin/customize-layout.sh
RUN chmod +x /usr/local/bin/init-cachet-customization.sh

# Create custom entrypoint that applies customizations
COPY entrypoint-custom.sh /usr/local/bin/entrypoint-custom.sh
RUN chmod +x /usr/local/bin/entrypoint-custom.sh

# Set custom entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint-custom.sh"]
CMD ["apache2-foreground"]
