<div class="custom-header-section">
    <div class="container">
        <div class="row">
            <div class="col-md-12 text-center">
                <h1 class="app-title">
                    <span class="app-icon">🚀</span>
                    TurdParty
                </h1>
                <p class="app-description">
                    File Upload & VM Injection Platform
                </p>
                <div class="service-summary">
                    <div class="service-stats">
                        <div class="stat-item">
                            <span class="stat-number" id="total-services">6</span>
                            <span class="stat-label">Services</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="operational-services">6</span>
                            <span class="stat-label">Operational</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="service-groups">3</span>
                            <span class="stat-label">Categories</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update service statistics dynamically
document.addEventListener('DOMContentLoaded', function() {
    // Fetch component data from Cachet API
    fetch('/api/v1/components')
        .then(response => response.json())
        .then(data => {
            const components = data.data || [];
            const totalServices = components.length;
            const operationalServices = components.filter(c => c.status === 1).length;
            
            // Update the statistics
            document.getElementById('total-services').textContent = totalServices;
            document.getElementById('operational-services').textContent = operationalServices;
            
            // Update service groups count
            fetch('/api/v1/components/groups')
                .then(response => response.json())
                .then(groupData => {
                    const groups = groupData.data || [];
                    document.getElementById('service-groups').textContent = groups.length;
                })
                .catch(error => {
                    console.log('Could not fetch groups data');
                });
        })
        .catch(error => {
            console.log('Could not fetch components data');
        });
});
</script>
