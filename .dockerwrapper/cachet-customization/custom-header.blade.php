<div class="custom-header-section">
    <div class="container">
        <div class="row">
            <div class="col-md-12 text-center">
                <h1 class="app-title">
                    <span class="app-icon">🚀</span>
                    TurdParty
                </h1>
                <p class="app-description">
                    File Upload & VM Injection Platform - Real-time Service Status
                </p>
                <div class="service-summary">
                    <div class="service-stats">
                        <div class="stat-item">
                            <span class="stat-number" id="total-services">13</span>
                            <span class="stat-label">Services</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="operational-services">-</span>
                            <span class="stat-label">Operational</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="degraded-services">-</span>
                            <span class="stat-label">Degraded</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="service-groups">5</span>
                            <span class="stat-label">Categories</span>
                        </div>
                    </div>
                    <div class="service-categories">
                        <div class="category-item">
                            <span class="category-icon">🌐</span>
                            <span class="category-name">Frontend</span>
                        </div>
                        <div class="category-item">
                            <span class="category-icon">🚀</span>
                            <span class="category-name">API Services</span>
                        </div>
                        <div class="category-item">
                            <span class="category-icon">⚙️</span>
                            <span class="category-name">Workers</span>
                        </div>
                        <div class="category-item">
                            <span class="category-icon">🗄️</span>
                            <span class="category-name">Storage</span>
                        </div>
                        <div class="category-item">
                            <span class="category-icon">🔧</span>
                            <span class="category-name">Monitoring</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced service statistics for TurdParty
document.addEventListener('DOMContentLoaded', function() {
    // Fetch component data from Cachet API
    fetch('/api/v1/components')
        .then(response => response.json())
        .then(data => {
            const components = data.data || [];
            const totalServices = components.length;
            const operationalServices = components.filter(c => c.status === 1).length;
            const degradedServices = components.filter(c => c.status === 2 || c.status === 3 || c.status === 4).length;

            // Animate the statistics
            animateCounter('total-services', totalServices);
            animateCounter('operational-services', operationalServices);
            animateCounter('degraded-services', degradedServices);

            // Update service groups count
            fetch('/api/v1/components/groups')
                .then(response => response.json())
                .then(groupData => {
                    const groups = groupData.data || [];
                    animateCounter('service-groups', groups.length);
                })
                .catch(error => {
                    console.log('Could not fetch groups data');
                });
        })
        .catch(error => {
            console.log('Could not fetch components data');
        });
});

function animateCounter(elementId, finalValue) {
    const element = document.getElementById(elementId);
    if (!element) return;

    let currentValue = 0;
    const increment = Math.max(1, Math.ceil(finalValue / 20));

    const timer = setInterval(() => {
        currentValue += increment;
        if (currentValue >= finalValue) {
            currentValue = finalValue;
            clearInterval(timer);
        }
        element.textContent = currentValue;
    }, 50);
}
</script>
