<div class="custom-header-section">
    <div class="container">
        <div class="row">
            <div class="col-md-12 text-center">
                <h1 class="app-title">
                    <span class="app-icon">🚀</span>
                    TurdParty
                </h1>
                <p class="app-description">
                    File Upload & VM Injection Platform - Real-time Service Status
                </p>
                <div class="service-summary">
                    <div class="service-stats">
                        <div class="stat-item">
                            <span class="stat-number" id="total-services">13</span>
                            <span class="stat-label">Services</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="operational-services">-</span>
                            <span class="stat-label">Operational</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="degraded-services">-</span>
                            <span class="stat-label">Degraded</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="service-groups">5</span>
                            <span class="stat-label">Categories</span>
                        </div>
                    </div>
                    <div class="service-categories">
                        <div class="category-item">
                            <span class="category-icon">🌐</span>
                            <span class="category-name">Frontend</span>
                        </div>
                        <div class="category-item">
                            <span class="category-icon">🚀</span>
                            <span class="category-name">API Services</span>
                        </div>
                        <div class="category-item">
                            <span class="category-icon">⚙️</span>
                            <span class="category-name">Workers</span>
                        </div>
                        <div class="category-item">
                            <span class="category-icon">🗄️</span>
                            <span class="category-name">Storage</span>
                        </div>
                        <div class="category-item">
                            <span class="category-icon">🔧</span>
                            <span class="category-name">Monitoring</span>
                        </div>
                    </div>
                </div>

                <!-- TurdParty Architecture Diagram -->
                <div class="architecture-section">
                    <h3 class="architecture-title">
                        <span class="architecture-icon">🏗️</span>
                        System Architecture
                    </h3>
                    <div class="mermaid-container">
                        <div class="mermaid" id="turdparty-architecture">
                            graph TB
                                %% Frontend Layer
                                subgraph "🌐 Frontend & User Interface"
                                    UI[🌐 React Frontend<br/>Web Application]
                                end

                                %% API Layer
                                subgraph "🚀 API & Application Services"
                                    API[🚀 FastAPI Backend<br/>Main Application]
                                    AUTH[🔐 Authentication<br/>User Security]
                                    UPLOAD[📁 File Upload<br/>File Processing]
                                    VM[🖥️ VM Management<br/>Vagrant Operations]
                                end

                                %% Worker Layer
                                subgraph "⚙️ Task Processing & Workers"
                                    CELERY_DEFAULT[⚙️ Default Worker<br/>General Tasks]
                                    CELERY_FILE[📂 File Worker<br/>File Operations]
                                    CELERY_VM[🖥️ VM Worker<br/>VM Provisioning]
                                end

                                %% Storage Layer
                                subgraph "🗄️ Data Storage & Infrastructure"
                                    POSTGRES[🗄️ PostgreSQL<br/>Primary Database]
                                    MINIO[📦 MinIO<br/>Object Storage]
                                    MINIO_SSH[🔗 MinIO SSH<br/>Secure Gateway]
                                    REDIS[⚡ Redis<br/>Cache & Queue]
                                end

                                %% Monitoring Layer
                                subgraph "🔧 Monitoring & Management"
                                    FLOWER[🌸 Celery Flower<br/>Worker Monitor]
                                    CACHET[📊 Cachet<br/>Status Page]
                                end

                                %% User Interactions
                                USER[👤 User] --> UI
                                UI --> API

                                %% API Interactions
                                API --> AUTH
                                API --> UPLOAD
                                API --> VM
                                API --> POSTGRES
                                API --> REDIS

                                %% File Upload Flow
                                UPLOAD --> MINIO
                                UPLOAD --> CELERY_FILE
                                CELERY_FILE --> MINIO
                                CELERY_FILE --> MINIO_SSH

                                %% VM Management Flow
                                VM --> CELERY_VM
                                CELERY_VM --> MINIO

                                %% Worker Coordination
                                CELERY_DEFAULT --> REDIS
                                CELERY_FILE --> REDIS
                                CELERY_VM --> REDIS

                                %% Data Persistence
                                CELERY_DEFAULT --> POSTGRES
                                CELERY_FILE --> POSTGRES
                                CELERY_VM --> POSTGRES

                                %% Monitoring
                                FLOWER --> CELERY_DEFAULT
                                FLOWER --> CELERY_FILE
                                FLOWER --> CELERY_VM
                                CACHET -.-> API
                                CACHET -.-> POSTGRES
                                CACHET -.-> REDIS
                                CACHET -.-> MINIO

                                %% Styling
                                classDef frontend fill:#4a90e2,stroke:#357abd,stroke-width:2px,color:#fff
                                classDef api fill:#7b68ee,stroke:#6a5acd,stroke-width:2px,color:#fff
                                classDef worker fill:#ff9500,stroke:#e6851e,stroke-width:2px,color:#fff
                                classDef storage fill:#50c878,stroke:#45b369,stroke-width:2px,color:#fff
                                classDef monitoring fill:#ff6b6b,stroke:#e55a5a,stroke-width:2px,color:#fff
                                classDef user fill:#ffd93d,stroke:#e6c441,stroke-width:2px,color:#333

                                class UI frontend
                                class API,AUTH,UPLOAD,VM api
                                class CELERY_DEFAULT,CELERY_FILE,CELERY_VM worker
                                class POSTGRES,MINIO,MINIO_SSH,REDIS storage
                                class FLOWER,CACHET monitoring
                                class USER user
                        </div>
                    </div>
                    <div class="architecture-legend">
                        <div class="legend-item">
                            <span class="legend-color frontend-color"></span>
                            <span class="legend-text">Frontend Layer</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color api-color"></span>
                            <span class="legend-text">API Services</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color worker-color"></span>
                            <span class="legend-text">Workers</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color storage-color"></span>
                            <span class="legend-text">Storage</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color monitoring-color"></span>
                            <span class="legend-text">Monitoring</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced service statistics for TurdParty
document.addEventListener('DOMContentLoaded', function() {
    // Fetch component data from Cachet API
    fetch('/api/v1/components')
        .then(response => response.json())
        .then(data => {
            const components = data.data || [];
            const totalServices = components.length;
            const operationalServices = components.filter(c => c.status === 1).length;
            const degradedServices = components.filter(c => c.status === 2 || c.status === 3 || c.status === 4).length;

            // Animate the statistics
            animateCounter('total-services', totalServices);
            animateCounter('operational-services', operationalServices);
            animateCounter('degraded-services', degradedServices);

            // Update service groups count
            fetch('/api/v1/components/groups')
                .then(response => response.json())
                .then(groupData => {
                    const groups = groupData.data || [];
                    animateCounter('service-groups', groups.length);
                })
                .catch(error => {
                    console.log('Could not fetch groups data');
                });
        })
        .catch(error => {
            console.log('Could not fetch components data');
        });
});

function animateCounter(elementId, finalValue) {
    const element = document.getElementById(elementId);
    if (!element) return;

    let currentValue = 0;
    const increment = Math.max(1, Math.ceil(finalValue / 20));

    const timer = setInterval(() => {
        currentValue += increment;
        if (currentValue >= finalValue) {
            currentValue = finalValue;
            clearInterval(timer);
        }
        element.textContent = currentValue;
    }, 50);
}
</script>

<!-- Mermaid.js for Architecture Diagrams (Local) -->
<script src="{{ asset('js/mermaid.min.js') }}"></script>
<!-- Status Indicators for Components -->
<script src="{{ asset('js/status-indicators.js') }}"></script>
<script>
// Initialize Mermaid with dark theme
document.addEventListener('DOMContentLoaded', function() {
    mermaid.initialize({
        theme: 'dark',
        themeVariables: {
            primaryColor: '#4a90e2',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#357abd',
            lineColor: '#6c757d',
            secondaryColor: '#7b68ee',
            tertiaryColor: '#ff9500',
            background: '#2d3748',
            mainBkg: '#2d3748',
            secondBkg: '#4a5568',
            tertiaryBkg: '#718096'
        },
        startOnLoad: true,
        flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
        },
        securityLevel: 'loose'
    });

    // Re-render mermaid diagrams after initialization
    setTimeout(() => {
        mermaid.init(undefined, '.mermaid');
    }, 500);
});
</script>
