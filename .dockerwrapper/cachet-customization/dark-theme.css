/* TurdParty Cachet Dark Mode Theme */
/* Custom dark mode styling for Cachet status page */

/* Root variables for dark theme */
:root {
  --dark-bg-primary: #1a1a1a;
  --dark-bg-secondary: #2d2d2d;
  --dark-bg-tertiary: #3a3a3a;
  --dark-text-primary: #ffffff;
  --dark-text-secondary: #e0e0e0;
  --dark-text-muted: #a0a0a0;
  --dark-border: #404040;
  --dark-accent: #4a9eff;
  --dark-success: #52c41a;
  --dark-warning: #faad14;
  --dark-error: #ff4d4f;
  --dark-info: #1890ff;
}

/* Apply dark theme by default */
body.status-page {
  background-color: var(--dark-bg-primary) !important;
  color: var(--dark-text-primary) !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Container and layout */
.container {
  background-color: var(--dark-bg-primary) !important;
}

/* Header and banner */
.app-banner {
  background-color: var(--dark-bg-secondary) !important;
  border-bottom: 1px solid var(--dark-border) !important;
}

.app-banner h1,
.app-banner .lead {
  color: var(--dark-text-primary) !important;
}

/* Status indicators */
.status-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* Component groups */
.component-group {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border) !important;
  border-radius: 8px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.component-group h4 {
  color: var(--dark-text-primary) !important;
  background-color: var(--dark-bg-tertiary) !important;
  border-bottom: 1px solid var(--dark-border) !important;
  padding: 15px 20px !important;
  margin: 0 !important;
  border-radius: 8px 8px 0 0 !important;
  font-weight: 600 !important;
}

/* Individual components */
.component {
  background-color: var(--dark-bg-secondary) !important;
  border-bottom: 1px solid var(--dark-border) !important;
  padding: 15px 20px !important;
  transition: background-color 0.2s ease !important;
}

.component:hover {
  background-color: var(--dark-bg-tertiary) !important;
}

.component:last-child {
  border-bottom: none !important;
  border-radius: 0 0 8px 8px !important;
}

.component-name {
  color: var(--dark-text-primary) !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
}

.component-description {
  color: var(--dark-text-secondary) !important;
  font-size: 14px !important;
  margin-top: 4px !important;
}

/* Status badges */
.component-status {
  display: inline-flex !important;
  align-items: center !important;
  padding: 4px 12px !important;
  border-radius: 20px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.component-status.status-1 {
  background-color: rgba(82, 196, 26, 0.15) !important;
  color: var(--dark-success) !important;
  border: 1px solid rgba(82, 196, 26, 0.3) !important;
}

.component-status.status-2 {
  background-color: rgba(250, 173, 20, 0.15) !important;
  color: var(--dark-warning) !important;
  border: 1px solid rgba(250, 173, 20, 0.3) !important;
}

.component-status.status-3 {
  background-color: rgba(255, 77, 79, 0.15) !important;
  color: var(--dark-error) !important;
  border: 1px solid rgba(255, 77, 79, 0.3) !important;
}

.component-status.status-4 {
  background-color: rgba(255, 77, 79, 0.15) !important;
  color: var(--dark-error) !important;
  border: 1px solid rgba(255, 77, 79, 0.3) !important;
}

/* Overall status */
.status-section {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border) !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 30px !important;
  text-align: center !important;
}

.status-section h1 {
  color: var(--dark-text-primary) !important;
  margin-bottom: 10px !important;
}

.status-section .lead {
  color: var(--dark-text-secondary) !important;
}

/* Incidents */
.incident {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border) !important;
  border-radius: 8px !important;
  margin-bottom: 15px !important;
  padding: 20px !important;
}

.incident-title {
  color: var(--dark-text-primary) !important;
  font-weight: 600 !important;
}

.incident-content {
  color: var(--dark-text-secondary) !important;
  margin-top: 10px !important;
}

/* Metrics */
.metric {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border) !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 20px !important;
}

.metric h4 {
  color: var(--dark-text-primary) !important;
}

/* Footer and bottom elements */
.footer,
footer,
.app-footer,
.status-footer,
.bottom-bar,
.navbar-fixed-bottom,
.navbar-bottom {
  background-color: var(--dark-bg-secondary) !important;
  border-top: 1px solid var(--dark-border) !important;
  color: var(--dark-text-primary) !important;
  padding: 20px 0 !important;
  margin-top: 40px !important;
}

.footer a,
footer a,
.app-footer a,
.status-footer a,
.bottom-bar a {
  color: var(--dark-accent) !important;
}

.footer a:hover,
footer a:hover,
.app-footer a:hover,
.status-footer a:hover,
.bottom-bar a:hover {
  color: var(--dark-text-primary) !important;
}

/* Bottom navigation and status bars */
.navbar-bottom,
.navbar-fixed-bottom,
.status-bar-bottom {
  background-color: var(--dark-bg-secondary) !important;
  border-top: 1px solid var(--dark-border) !important;
}

.navbar-bottom .navbar-nav > li > a,
.navbar-fixed-bottom .navbar-nav > li > a {
  color: var(--dark-text-primary) !important;
}

.navbar-bottom .navbar-nav > li > a:hover,
.navbar-fixed-bottom .navbar-nav > li > a:hover {
  color: var(--dark-accent) !important;
  background-color: var(--dark-bg-tertiary) !important;
}

/* Service Icons */
.service-icon {
  width: 20px !important;
  height: 20px !important;
  margin-right: 10px !important;
  vertical-align: middle !important;
  filter: brightness(0.9) !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .component-group {
    margin-bottom: 15px !important;
  }
  
  .component {
    padding: 12px 15px !important;
  }
  
  .component-group h4 {
    padding: 12px 15px !important;
    font-size: 16px !important;
  }
}

/* Scrollbar styling for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-bg-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--dark-bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--dark-border);
}

/* Links */
a {
  color: var(--dark-accent) !important;
}

a:hover {
  color: var(--dark-text-primary) !important;
}

/* Tables */
table {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
}

th {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
  border-color: var(--dark-border) !important;
}

td {
  border-color: var(--dark-border) !important;
  color: var(--dark-text-secondary) !important;
}

/* Forms */
input, textarea, select {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
  border-color: var(--dark-border) !important;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--dark-accent) !important;
  box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2) !important;
}

/* Buttons */
.btn {
  border-radius: 6px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.btn-primary {
  background-color: var(--dark-accent) !important;
  border-color: var(--dark-accent) !important;
}

.btn-primary:hover {
  background-color: #357abd !important;
  border-color: #357abd !important;
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.component-group {
  animation: fadeIn 0.3s ease-out !important;
}

/* Additional comprehensive dark mode overrides */
/* Catch all possible light elements */
.panel,
.panel-default,
.panel-body,
.panel-heading,
.well,
.jumbotron,
.alert,
.modal,
.modal-content,
.modal-header,
.modal-body,
.modal-footer,
.dropdown-menu,
.popover,
.tooltip-inner,
.navbar,
.navbar-default,
.navbar-inverse,
.breadcrumb {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
  border-color: var(--dark-border) !important;
}

/* Specific fixes for component groups */
.list-group,
.list-group-item,
.component-group,
.component-group-wrapper,
.status-page .list-group,
.status-page .list-group-item,
.status-page .panel,
.status-page .panel-default,
.status-page .panel-body,
.status-page .panel-heading {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
  border-color: var(--dark-border) !important;
}

/* Component group headers */
.list-group-item.list-group-item-heading,
.panel-heading,
.component-group-heading {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
  border-bottom: 1px solid var(--dark-border) !important;
}

/* Text elements with better contrast */
p, span, div, li, td, th, label, small, .text-muted, .help-block {
  color: var(--dark-text-secondary) !important;
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
  color: var(--dark-text-primary) !important;
}

/* Strong text elements */
strong, b, .font-weight-bold, .fw-bold {
  color: var(--dark-text-primary) !important;
}

/* All bottom/footer elements - comprehensive catch */
[class*="footer"],
[class*="bottom"],
[id*="footer"],
[id*="bottom"],
.fixed-bottom,
.sticky-bottom,
.position-fixed.bottom-0,
.position-sticky.bottom-0 {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
  border-top: 1px solid var(--dark-border) !important;
}

/* Ensure all text in bottom elements is visible */
[class*="footer"] *,
[class*="bottom"] *,
[id*="footer"] *,
[id*="bottom"] * {
  color: var(--dark-text-secondary) !important;
}

[class*="footer"] a,
[class*="bottom"] a,
[id*="footer"] a,
[id*="bottom"] a {
  color: var(--dark-accent) !important;
}

/* Print styles */
@media print {
  body.status-page {
    background-color: white !important;
    color: black !important;
  }
}
