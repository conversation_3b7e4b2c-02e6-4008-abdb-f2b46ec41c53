/* TurdParty Cachet Dark Mode Theme */
/* Custom dark mode styling for Cachet status page */

/* Root variables for dark theme */
:root {
  --dark-bg-primary: #1a1a1a;
  --dark-bg-secondary: #2d2d2d;
  --dark-bg-tertiary: #3a3a3a;
  --dark-text-primary: #ffffff;
  --dark-text-secondary: #e0e0e0;
  --dark-text-muted: #a0a0a0;
  --dark-border: #404040;
  --dark-accent: #4a9eff;
  --dark-success: #52c41a;
  --dark-warning: #faad14;
  --dark-error: #ff4d4f;
  --dark-info: #1890ff;
}

/* Apply dark theme by default */
body.status-page {
  background-color: var(--dark-bg-primary) !important;
  color: var(--dark-text-primary) !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Container and layout */
.container {
  background-color: var(--dark-bg-primary) !important;
}

/* Header and banner */
.app-banner {
  background-color: var(--dark-bg-secondary) !important;
  border-bottom: 1px solid var(--dark-border) !important;
}

.app-banner h1,
.app-banner .lead {
  color: var(--dark-text-primary) !important;
}

/* Status indicators */
.status-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* Component groups */
.component-group {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border) !important;
  border-radius: 8px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.component-group h4 {
  color: var(--dark-text-primary) !important;
  background-color: var(--dark-bg-tertiary) !important;
  border-bottom: 1px solid var(--dark-border) !important;
  padding: 15px 20px !important;
  margin: 0 !important;
  border-radius: 8px 8px 0 0 !important;
  font-weight: 600 !important;
}

/* Individual components */
.component {
  background-color: var(--dark-bg-secondary) !important;
  border-bottom: 1px solid var(--dark-border) !important;
  padding: 15px 20px !important;
  transition: background-color 0.2s ease !important;
  position: relative !important;
}

.component:hover {
  background-color: var(--dark-bg-tertiary) !important;
}

.component:last-child {
  border-bottom: none !important;
  border-radius: 0 0 8px 8px !important;
}

/* Status Icons for Components */
.component::before {
  content: '' !important;
  position: absolute !important;
  left: 15px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  border: 2px solid transparent !important;
  z-index: 10 !important;
}

/* Operational - Green */
.component[data-status="1"]::before,
.component.status-1::before {
  background-color: #52c41a !important;
  border-color: #389e0d !important;
  box-shadow: 0 0 8px rgba(82, 196, 26, 0.4) !important;
}

/* Performance Issues - Amber/Yellow */
.component[data-status="2"]::before,
.component.status-2::before {
  background-color: #faad14 !important;
  border-color: #d48806 !important;
  box-shadow: 0 0 8px rgba(250, 173, 20, 0.4) !important;
}

/* Partial Outage - Orange */
.component[data-status="3"]::before,
.component.status-3::before {
  background-color: #ff7a45 !important;
  border-color: #d4380d !important;
  box-shadow: 0 0 8px rgba(255, 122, 69, 0.4) !important;
}

/* Major Outage - Red */
.component[data-status="4"]::before,
.component.status-4::before {
  background-color: #ff4d4f !important;
  border-color: #cf1322 !important;
  box-shadow: 0 0 8px rgba(255, 77, 79, 0.4) !important;
}

/* Adjust component content to make room for status icon */
.component-name {
  margin-left: 25px !important;
  position: relative !important;
}

.component-description {
  margin-left: 25px !important;
}

/* Status Badges */
.status-badge {
  display: inline-block !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  font-size: 11px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  margin-left: 10px !important;
  border: 1px solid transparent !important;
}

.status-badge.status-1 {
  background-color: rgba(82, 196, 26, 0.15) !important;
  color: #52c41a !important;
  border-color: rgba(82, 196, 26, 0.3) !important;
}

.status-badge.status-2 {
  background-color: rgba(250, 173, 20, 0.15) !important;
  color: #faad14 !important;
  border-color: rgba(250, 173, 20, 0.3) !important;
}

.status-badge.status-3 {
  background-color: rgba(255, 122, 69, 0.15) !important;
  color: #ff7a45 !important;
  border-color: rgba(255, 122, 69, 0.3) !important;
}

.status-badge.status-4 {
  background-color: rgba(255, 77, 79, 0.15) !important;
  color: #ff4d4f !important;
  border-color: rgba(255, 77, 79, 0.3) !important;
}

/* Components Section */
.components-section {
  margin: 40px 0 !important;
  padding: 0 20px !important;
}

.components-container {
  max-width: 1200px !important;
  margin: 0 auto !important;
}

.components-title {
  color: #e2e8f0 !important;
  font-size: 2rem !important;
  font-weight: 600 !important;
  margin-bottom: 30px !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 15px !important;
}

.components-icon {
  font-size: 2.5rem !important;
}

.components-list {
  display: grid !important;
  gap: 20px !important;
}

.component-group {
  background-color: #2d3748 !important;
  border: 1px solid #4a5568 !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.component-group-title {
  background: linear-gradient(90deg, #4a5568 0%, #2d3748 100%) !important;
  padding: 15px 20px !important;
  margin: 0 !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  color: #e2e8f0 !important;
  border-bottom: 1px solid #4a5568 !important;
}

.component {
  background-color: #2d3748 !important;
  border-bottom: 1px solid #4a5568 !important;
  padding: 15px 20px !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  cursor: pointer !important;
}

.component:hover {
  background-color: #4a5568 !important;
  transform: translateX(2px) !important;
}

.component:last-child {
  border-bottom: none !important;
}

/* Status Icons for Components */
.component::before {
  content: '' !important;
  position: absolute !important;
  left: 15px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  border: 2px solid transparent !important;
  z-index: 10 !important;
  transition: all 0.3s ease !important;
}

.component[data-status="1"]::before,
.component.status-1::before {
  background-color: #52c41a !important;
  border-color: #389e0d !important;
  box-shadow: 0 0 12px rgba(82, 196, 26, 0.6) !important;
}

.component[data-status="2"]::before,
.component.status-2::before {
  background-color: #faad14 !important;
  border-color: #d48806 !important;
  box-shadow: 0 0 12px rgba(250, 173, 20, 0.6) !important;
}

.component[data-status="3"]::before,
.component.status-3::before {
  background-color: #ff7a45 !important;
  border-color: #d4380d !important;
  box-shadow: 0 0 12px rgba(255, 122, 69, 0.6) !important;
}

.component[data-status="4"]::before,
.component.status-4::before {
  background-color: #ff4d4f !important;
  border-color: #cf1322 !important;
  box-shadow: 0 0 12px rgba(255, 77, 79, 0.6) !important;
  animation: pulse-red 2s infinite !important;
}

@keyframes pulse-red {
  0%, 100% {
    box-shadow: 0 0 12px rgba(255, 77, 79, 0.6) !important;
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 77, 79, 0.9) !important;
  }
}

.loading-components,
.no-components,
.error-components {
  text-align: center !important;
  padding: 40px 20px !important;
  color: #a0aec0 !important;
  font-style: italic !important;
}

.error-components {
  color: #ff4d4f !important;
}

/* Import Working Status Indicators CSS */
@import url('/css/status-indicators.css');

.component-name {
  color: var(--dark-text-primary) !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
}

.component-description {
  color: var(--dark-text-secondary) !important;
  font-size: 14px !important;
  margin-top: 4px !important;
}

/* Status badges */
.component-status {
  display: inline-flex !important;
  align-items: center !important;
  padding: 4px 12px !important;
  border-radius: 20px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.component-status.status-1 {
  background-color: rgba(82, 196, 26, 0.15) !important;
  color: var(--dark-success) !important;
  border: 1px solid rgba(82, 196, 26, 0.3) !important;
}

.component-status.status-2 {
  background-color: rgba(250, 173, 20, 0.15) !important;
  color: var(--dark-warning) !important;
  border: 1px solid rgba(250, 173, 20, 0.3) !important;
}

.component-status.status-3 {
  background-color: rgba(255, 77, 79, 0.15) !important;
  color: var(--dark-error) !important;
  border: 1px solid rgba(255, 77, 79, 0.3) !important;
}

.component-status.status-4 {
  background-color: rgba(255, 77, 79, 0.15) !important;
  color: var(--dark-error) !important;
  border: 1px solid rgba(255, 77, 79, 0.3) !important;
}

/* Overall status */
.status-section {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border) !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 30px !important;
  text-align: center !important;
}

.status-section h1 {
  color: var(--dark-text-primary) !important;
  margin-bottom: 10px !important;
}

.status-section .lead {
  color: var(--dark-text-secondary) !important;
}

/* Incidents */
.incident {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border) !important;
  border-radius: 8px !important;
  margin-bottom: 15px !important;
  padding: 20px !important;
}

.incident-title {
  color: var(--dark-text-primary) !important;
  font-weight: 600 !important;
}

.incident-content {
  color: var(--dark-text-secondary) !important;
  margin-top: 10px !important;
}

/* Metrics */
.metric {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border) !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 20px !important;
}

.metric h4 {
  color: var(--dark-text-primary) !important;
}

/* Footer and bottom elements */
.footer,
footer,
.app-footer,
.status-footer,
.bottom-bar,
.navbar-fixed-bottom,
.navbar-bottom {
  background-color: var(--dark-bg-secondary) !important;
  border-top: 1px solid var(--dark-border) !important;
  color: var(--dark-text-primary) !important;
  padding: 20px 0 !important;
  margin-top: 40px !important;
}

.footer a,
footer a,
.app-footer a,
.status-footer a,
.bottom-bar a {
  color: var(--dark-accent) !important;
}

.footer a:hover,
footer a:hover,
.app-footer a:hover,
.status-footer a:hover,
.bottom-bar a:hover {
  color: var(--dark-text-primary) !important;
}

/* Bottom navigation and status bars */
.navbar-bottom,
.navbar-fixed-bottom,
.status-bar-bottom {
  background-color: var(--dark-bg-secondary) !important;
  border-top: 1px solid var(--dark-border) !important;
}

.navbar-bottom .navbar-nav > li > a,
.navbar-fixed-bottom .navbar-nav > li > a {
  color: var(--dark-text-primary) !important;
}

.navbar-bottom .navbar-nav > li > a:hover,
.navbar-fixed-bottom .navbar-nav > li > a:hover {
  color: var(--dark-accent) !important;
  background-color: var(--dark-bg-tertiary) !important;
}

/* Service Icons */
.service-icon {
  width: 20px !important;
  height: 20px !important;
  margin-right: 10px !important;
  vertical-align: middle !important;
  filter: brightness(0.9) !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .component-group {
    margin-bottom: 15px !important;
  }
  
  .component {
    padding: 12px 15px !important;
  }
  
  .component-group h4 {
    padding: 12px 15px !important;
    font-size: 16px !important;
  }
}

/* Scrollbar styling for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-bg-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--dark-bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--dark-border);
}

/* Links */
a {
  color: var(--dark-accent) !important;
}

a:hover {
  color: var(--dark-text-primary) !important;
}

/* Tables */
table {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
}

th {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
  border-color: var(--dark-border) !important;
}

td {
  border-color: var(--dark-border) !important;
  color: var(--dark-text-secondary) !important;
}

/* Forms */
input, textarea, select {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
  border-color: var(--dark-border) !important;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--dark-accent) !important;
  box-shadow: 0 0 0 2px rgba(74, 158, 255, 0.2) !important;
}

/* Buttons */
.btn {
  border-radius: 6px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.btn-primary {
  background-color: var(--dark-accent) !important;
  border-color: var(--dark-accent) !important;
}

.btn-primary:hover {
  background-color: #357abd !important;
  border-color: #357abd !important;
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.component-group {
  animation: fadeIn 0.3s ease-out !important;
}

/* Additional comprehensive dark mode overrides */
/* Catch all possible light elements */
.panel,
.panel-default,
.panel-body,
.panel-heading,
.well,
.jumbotron,
.alert,
.modal,
.modal-content,
.modal-header,
.modal-body,
.modal-footer,
.dropdown-menu,
.popover,
.tooltip-inner,
.navbar,
.navbar-default,
.navbar-inverse,
.breadcrumb {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
  border-color: var(--dark-border) !important;
}

/* Specific fixes for component groups */
.list-group,
.list-group-item,
.component-group,
.component-group-wrapper,
.status-page .list-group,
.status-page .list-group-item,
.status-page .panel,
.status-page .panel-default,
.status-page .panel-body,
.status-page .panel-heading {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
  border-color: var(--dark-border) !important;
}

/* Component group headers */
.list-group-item.list-group-item-heading,
.panel-heading,
.component-group-heading {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
  border-bottom: 1px solid var(--dark-border) !important;
}

/* Text elements with better contrast */
p, span, div, li, td, th, label, small, .text-muted, .help-block {
  color: var(--dark-text-secondary) !important;
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
  color: var(--dark-text-primary) !important;
}

/* Strong text elements */
strong, b, .font-weight-bold, .fw-bold {
  color: var(--dark-text-primary) !important;
}

/* All bottom/footer elements - comprehensive catch */
[class*="footer"],
[class*="bottom"],
[id*="footer"],
[id*="bottom"],
.fixed-bottom,
.sticky-bottom,
.position-fixed.bottom-0,
.position-sticky.bottom-0 {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
  border-top: 1px solid var(--dark-border) !important;
}

/* Ensure all text in bottom elements is visible */
[class*="footer"] *,
[class*="bottom"] *,
[id*="footer"] *,
[id*="bottom"] * {
  color: var(--dark-text-secondary) !important;
}

[class*="footer"] a,
[class*="bottom"] a,
[id*="footer"] a,
[id*="bottom"] a {
  color: var(--dark-accent) !important;
}

/* Enhanced App Branding */
.custom-header-section {
  background: linear-gradient(135deg, var(--dark-bg-secondary) 0%, var(--dark-bg-tertiary) 100%) !important;
  border-bottom: 2px solid var(--dark-border) !important;
  padding: 40px 0 !important;
  margin-bottom: 30px !important;
  border-radius: 0 0 12px 12px !important;
}

.app-title {
  color: var(--dark-text-primary) !important;
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 10px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 15px !important;
}

.app-icon {
  font-size: 3rem !important;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3)) !important;
}

.app-description {
  color: var(--dark-text-secondary) !important;
  font-size: 1.2rem !important;
  margin-bottom: 25px !important;
  font-weight: 400 !important;
}

.service-summary {
  margin-top: 20px !important;
}

.service-stats {
  display: flex !important;
  justify-content: center !important;
  gap: 40px !important;
  flex-wrap: wrap !important;
}

.stat-item {
  text-align: center !important;
  padding: 15px 20px !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-radius: 8px !important;
  border: 1px solid var(--dark-border) !important;
  min-width: 100px !important;
  transition: all 0.3s ease !important;
}

.stat-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  transform: translateY(-2px) !important;
}

.stat-number {
  display: block !important;
  font-size: 2rem !important;
  font-weight: 700 !important;
  color: var(--dark-accent) !important;
  line-height: 1 !important;
}

.stat-label {
  display: block !important;
  font-size: 0.9rem !important;
  color: var(--dark-text-secondary) !important;
  margin-top: 5px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Enhanced component group styling */
.component-group h4 {
  background: linear-gradient(90deg, var(--dark-bg-tertiary) 0%, var(--dark-bg-secondary) 100%) !important;
  padding: 15px 20px !important;
  margin: 0 !important;
  border-radius: 8px 8px 0 0 !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
}

/* Responsive design for branding */
@media (max-width: 768px) {
  .app-title {
    font-size: 2rem !important;
    flex-direction: column !important;
    gap: 10px !important;
  }

  .app-icon {
    font-size: 2.5rem !important;
  }

  .service-stats {
    gap: 20px !important;
  }

  .stat-item {
    min-width: 80px !important;
    padding: 10px 15px !important;
  }

  .stat-number {
    font-size: 1.5rem !important;
  }
}

/* Enhanced animations */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.custom-header-section {
  animation: slideInFromTop 0.6s ease-out !important;
}

.stat-item {
  animation: slideInFromTop 0.8s ease-out !important;
}

.stat-item:nth-child(2) {
  animation-delay: 0.1s !important;
}

.stat-item:nth-child(3) {
  animation-delay: 0.2s !important;
}

/* Service Categories Display */
.service-categories {
  margin-top: 25px !important;
  display: flex !important;
  justify-content: center !important;
  gap: 20px !important;
  flex-wrap: wrap !important;
}

.category-item {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  padding: 10px 15px !important;
  background-color: rgba(255, 255, 255, 0.03) !important;
  border-radius: 6px !important;
  border: 1px solid var(--dark-border) !important;
  transition: all 0.3s ease !important;
  min-width: 80px !important;
}

.category-item:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
  transform: translateY(-1px) !important;
}

.category-icon {
  font-size: 1.5rem !important;
  margin-bottom: 5px !important;
}

.category-name {
  font-size: 0.8rem !important;
  color: var(--dark-text-secondary) !important;
  text-align: center !important;
  font-weight: 500 !important;
}

/* Enhanced stat items for degraded services */
.stat-item:has(#degraded-services) {
  border-color: rgba(255, 193, 7, 0.3) !important;
}

.stat-item:has(#degraded-services) .stat-number {
  color: #ffc107 !important;
}

/* Responsive design for categories */
@media (max-width: 768px) {
  .service-categories {
    gap: 15px !important;
  }

  .category-item {
    min-width: 70px !important;
    padding: 8px 12px !important;
  }

  .category-icon {
    font-size: 1.2rem !important;
  }

  .category-name {
    font-size: 0.7rem !important;
  }
}

/* Architecture Diagram Section */
.architecture-section {
  margin-top: 40px !important;
  padding: 30px 0 !important;
  border-top: 1px solid var(--dark-border) !important;
}

.architecture-title {
  color: var(--dark-text-primary) !important;
  font-size: 1.8rem !important;
  font-weight: 600 !important;
  margin-bottom: 25px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 12px !important;
}

.architecture-icon {
  font-size: 2rem !important;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3)) !important;
}

.mermaid-container {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border) !important;
  border-radius: 12px !important;
  padding: 25px !important;
  margin: 20px 0 !important;
  overflow-x: auto !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.mermaid {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 400px !important;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Mermaid diagram styling overrides */
.mermaid .node rect,
.mermaid .node circle,
.mermaid .node ellipse,
.mermaid .node polygon {
  stroke-width: 2px !important;
}

.mermaid .edgePath .path {
  stroke: var(--dark-text-secondary) !important;
  stroke-width: 2px !important;
}

.mermaid .arrowheadPath {
  fill: var(--dark-text-secondary) !important;
}

.mermaid .cluster rect {
  fill: rgba(255, 255, 255, 0.05) !important;
  stroke: var(--dark-border) !important;
  stroke-width: 1px !important;
  rx: 8px !important;
  ry: 8px !important;
}

.mermaid .cluster text {
  fill: var(--dark-text-primary) !important;
  font-weight: 600 !important;
  font-size: 14px !important;
}

.mermaid .nodeLabel {
  color: white !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  text-align: center !important;
}

/* Architecture Legend */
.architecture-legend {
  display: flex !important;
  justify-content: center !important;
  gap: 25px !important;
  flex-wrap: wrap !important;
  margin-top: 20px !important;
  padding: 15px !important;
  background-color: rgba(255, 255, 255, 0.03) !important;
  border-radius: 8px !important;
  border: 1px solid var(--dark-border) !important;
}

.legend-item {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 5px 10px !important;
  border-radius: 4px !important;
  transition: background-color 0.3s ease !important;
}

.legend-item:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

.legend-color {
  width: 16px !important;
  height: 16px !important;
  border-radius: 3px !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.frontend-color {
  background-color: #4a90e2 !important;
}

.api-color {
  background-color: #7b68ee !important;
}

.worker-color {
  background-color: #ff9500 !important;
}

.storage-color {
  background-color: #50c878 !important;
}

.monitoring-color {
  background-color: #ff6b6b !important;
}

.legend-text {
  color: var(--dark-text-secondary) !important;
  font-size: 0.9rem !important;
  font-weight: 500 !important;
}

/* Responsive design for architecture section */
@media (max-width: 768px) {
  .architecture-title {
    font-size: 1.5rem !important;
    flex-direction: column !important;
    gap: 8px !important;
  }

  .architecture-icon {
    font-size: 1.8rem !important;
  }

  .mermaid-container {
    padding: 15px !important;
    margin: 15px 0 !important;
  }

  .mermaid {
    min-height: 300px !important;
  }

  .architecture-legend {
    gap: 15px !important;
    padding: 10px !important;
  }

  .legend-item {
    padding: 3px 8px !important;
  }

  .legend-text {
    font-size: 0.8rem !important;
  }
}

/* Animation for architecture section */
.architecture-section {
  animation: slideInFromBottom 0.8s ease-out !important;
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Print styles */
@media print {
  body.status-page {
    background-color: white !important;
    color: black !important;
  }

  .architecture-section {
    page-break-inside: avoid !important;
  }
}
