#!/bin/bash

# Build custom Cachet image with dark theme and service icons
set -e

echo "🏗️  Building Custom Cachet with TurdParty Theme"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -d "cachet-customization" ]; then
    print_error "Please run this script from the .dockerwrapper directory"
    exit 1
fi

# Ensure all files are executable
print_status "Setting up file permissions..."
chmod +x cachet-customization/*.sh

# Build the custom Cachet image
print_status "Building custom Cachet Docker image..."
docker build -t turdparty/cachet-custom:latest ./cachet-customization/

if [ $? -eq 0 ]; then
    print_success "Custom Cachet image built successfully!"
else
    print_error "Failed to build custom Cachet image"
    exit 1
fi

# Check if the old Cachet container is running
if docker ps --format "{{.Names}}" | grep -q "turdparty_cachet"; then
    print_warning "Existing Cachet container found"
    
    read -p "Do you want to stop and replace it with the custom version? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Stopping existing Cachet container..."
        docker stop turdparty_cachet || true
        docker rm turdparty_cachet || true
        print_success "Existing container removed"
    else
        print_warning "Keeping existing container. Custom image is built but not deployed."
        exit 0
    fi
fi

# Start the custom Cachet service
print_status "Starting custom Cachet service..."
docker compose -f docker-compose.cachet-custom.yml up -d

# Wait for the service to be ready
print_status "Waiting for Cachet to be ready..."
max_wait=120
wait_time=0

while [ $wait_time -lt $max_wait ]; do
    if curl -s http://localhost:3501/api/v1/ping >/dev/null 2>&1; then
        print_success "Cachet is ready!"
        break
    fi
    
    sleep 2
    wait_time=$((wait_time + 2))
    echo -n "."
done

echo ""

if [ $wait_time -ge $max_wait ]; then
    print_error "Cachet did not start within $max_wait seconds"
    print_status "Checking logs..."
    docker logs turdparty_cachet_custom --tail 20
    exit 1
fi

# Verify the customizations
print_status "Verifying customizations..."

# Check if dark theme CSS is accessible
if curl -s http://localhost:3501/css/dark-theme.css >/dev/null 2>&1; then
    print_success "Dark theme CSS is accessible"
else
    print_warning "Dark theme CSS may not be accessible"
fi

# Check if service icons JS is accessible
if curl -s http://localhost:3501/js/service-icons.js >/dev/null 2>&1; then
    print_success "Service icons JS is accessible"
else
    print_warning "Service icons JS may not be accessible"
fi

# Final summary
echo ""
print_success "🎉 Custom Cachet Build Complete!"
echo ""
echo "🌐 Access URLs:"
echo "   • Cachet Status Page: http://localhost:3501"
echo ""
echo "✨ Features:"
echo "   • 🌙 Dark mode theme (build-time integrated)"
echo "   • 🎯 Service icons (automatic)"
echo "   • 😊 Emoji service names (pre-configured)"
echo "   • 📊 Component groups (auto-created)"
echo ""
echo "🔧 Management:"
echo "   • Theme files are baked into the image"
echo "   • Customizations apply automatically on startup"
echo "   • Database schema is initialized with groups and components"
echo ""

# Show running containers
print_status "Current Cachet containers:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(cachet|NAMES)"

print_success "Build and deployment complete! 🚀"
