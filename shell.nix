{ pkgs ? import <nixpkgs> {} }:

with pkgs;

let
  paramiko-2-11-0 = python3Packages.paramiko.overrideAttrs (oldAttrs: rec {
    version = "2.11.0";
    src = python3Packages.fetchPypi {
      pname = "paramiko";
      inherit version;
      sha256 = "003e6k7lk2vq2jxz3k423cfk3mqk0bw3g8sf5h3717aea1xkpx7c";
    };
  });

  fastapi-0-109-2 = python3Packages.fastapi.overrideAttrs (oldAttrs: rec {
    version = "0.109.2";
    src = python3Packages.fetchPypi {
      pname = "fastapi";
      inherit version;
      sha256 = "1d9x3xkzz3zj4mw82d7jz9100g0mxrz9rycj7zw4h4hkwxk20fgr";
    };
  });

  requests-2-31-0 = python3Packages.requests.overrideAttrs (oldAttrs: rec {
    version = "2.31.0";
    src = python3Packages.fetchPypi {
      pname = "requests";
      inherit version;
      sha256 = "1k2k3k4k5k6k7k8k9k0k1k2k3k4k5k6k7k8k9k0";
    };
  });
in

mkShell {
  buildInputs = [
    vagrant
    qemu_kvm
    libvirt
    python3
    python3Packages.pytest
    fastapi-0-109-2
    python3Packages.sqlalchemy
    requests-2-31-0
    paramiko-2-11-0
    python3Packages.httpx
    python3Packages.pyjwt
    python3Packages.cryptography
    python3Packages.pydantic-settings
    python3Packages.flask
    python3Packages.psycopg2
    python3Packages.asyncpg
    python3Packages.passlib
    python3Packages.email-validator
    python3Packages.boto3
    python3Packages.pyotp
    python3Packages.qrcode
    python3Packages.python-multipart
    python3Packages.minio
    python3Packages.pip
    python3Packages.pytest-asyncio
    python3Packages.aiosqlite
    python3Packages.coverage
    python3Packages.streamlit
    python3Packages.grpcio
    python3Packages.psutil
    python3Packages.rich
    python3Packages.textual
    python3Packages.typer
    python3Packages.docker
    python3Packages.pyyaml
    docker-compose
    gnumake
    nodejs_20
    nodePackages.npm
  ];

  shellHook = ''
    export NIXPKGS_ALLOW_UNFREE=1
    export VAGRANT_DEFAULT_PROVIDER="libvirt"
    export SSL_CERT_FILE="${cacert}/etc/ssl/certs/ca-bundle.crt"

    echo ""
    echo "TurdParty Development Environment"
    echo "=================================="
    echo "Available commands:"
    echo "  python service_monitor.py       - Run the service monitor with Rich UI"
    echo "  python service_monitor.py --tui - Run the service monitor with Textual TUI"
    echo ""
  '';
}