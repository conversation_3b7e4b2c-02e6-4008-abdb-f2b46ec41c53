"""
Test configuration settings.
"""
from pydantic_settings import BaseSettings

class TestSettings(BaseSettings):
    """Test settings."""
    TEST_MODE: bool = True
    TEST_USER_ID: str = "test-user"
    TEST_USER_EMAIL: str = "<EMAIL>"
    TEST_USER_PASSWORD: str = "password123"
    TEST_ADMIN_ID: str = "admin"
    TEST_ADMIN_EMAIL: str = "<EMAIL>"
    TEST_ADMIN_PASSWORD: str = "admin123"

    class Config:
        """Pydantic config."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

# Create a global instance of the settings
test_settings = TestSettings()
