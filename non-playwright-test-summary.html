<!DOCTYPE html>
<html>
<head>
    <title>Non-Playwright Test Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        .summary { margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-radius: 5px; }
        .passed { color: green; }
        .failed { color: red; }
        .test-group { margin-bottom: 30px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .progress-bar-container { width: 100%; background-color: #e0e0e0; border-radius: 4px; }
        .progress-bar { height: 24px; background-color: #4CAF50; border-radius: 4px; text-align: center; line-height: 24px; color: white; }
    </style>
</head>
<body>
    <h1>Non-Playwright Test Results</h1>
    <p>Run completed at Tue  8 Apr 14:53:29 CEST 2025</p>
    
    <div class="summary">
        <h2>Overall Summary</h2>
        <div class="progress-bar-container">
            <div class="progress-bar" style="width: 0%;">
                0% Passed
            </div>
        </div>
        <p class="passed">Passed: 1</p>
        <p class="failed">Failed: 235</p>
        <p>Total: 236</p>
    </div>
    
    <div class="test-group">
        <h2>Test Results by Category</h2>
        <table>
            <tr>
                <th>Category</th>
                <th>Passed</th>
                <th>Failed</th>
                <th>Total</th>
                <th>Pass Rate</th>
            </tr>
            <tr>
                <td>Python Tests</td>
                <td class="passed">0</td>
                <td class="failed">204</td>
                <td>204</td>
                <td>0%</td>
            </tr>
            <tr>
                <td>Shell Script Tests</td>
                <td class="passed">1</td>
                <td class="failed">10</td>
                <td>11</td>
                <td>9%</td>
            </tr>
            <tr>
                <td>NodeJS Tests</td>
                <td class="passed">0</td>
                <td class="failed">21</td>
                <td>21</td>
                <td>0%</td>
            </tr>
        </table>
    </div>
    
    <div class="links">
        <h2>Detailed Reports</h2>
        <ul>
            <li><a href="test_logs/non_playwright_test_run_20250408_145327.log">Full Test Log</a></li>
        </ul>
    </div>
</body>
</html>
