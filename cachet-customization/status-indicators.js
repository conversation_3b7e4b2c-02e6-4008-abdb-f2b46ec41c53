// Status Indicators Enhancement for TurdParty Cachet
(function() {
    'use strict';
    
    // Status color mappings
    const statusColors = {
        1: 'operational',    // Green
        2: 'performance',    // Amber/Yellow  
        3: 'partial',        // Orange
        4: 'major'          // Red
    };
    
    // Apply status indicators to components
    function applyStatusIndicators() {
        console.log('Applying status indicators...');
        
        // Fetch component data from Cachet API
        fetch('/api/v1/components')
            .then(response => response.json())
            .then(data => {
                const components = data.data || [];
                console.log('Found components:', components.length);
                
                // Apply status to each component element
                components.forEach(component => {
                    const componentElements = document.querySelectorAll('.component');
                    
                    componentElements.forEach(element => {
                        const nameElement = element.querySelector('.component-name');
                        if (nameElement) {
                            // Clean component name for comparison (remove emojis)
                            const cleanComponentName = component.name.replace(/[🌐🚀🔐🗄️⚡📦⚙️🌸🖥️📁🔗📊]/g, '').trim();
                            const cleanElementName = nameElement.textContent.replace(/[🌐🚀🔐🗄️⚡📦⚙️🌸🖥️📁🔗📊]/g, '').trim();
                            
                            if (cleanElementName.includes(cleanComponentName) || cleanComponentName.includes(cleanElementName)) {
                                // Add status data attribute
                                element.setAttribute('data-status', component.status);
                                element.classList.add(`status-${component.status}`);
                                
                                console.log(`Applied status ${component.status} to ${component.name}`);
                                
                                // Add status badge to component description
                                const descElement = element.querySelector('.component-description');
                                if (descElement) {
                                    const statusText = getStatusText(component.status);
                                    const statusBadge = `<span class="status-badge status-${component.status}">${statusText}</span>`;
                                    
                                    // Only add if not already present
                                    if (!descElement.innerHTML.includes('status-badge')) {
                                        descElement.innerHTML += ` ${statusBadge}`;
                                    }
                                }
                            }
                        }
                    });
                });
                
                console.log('Status indicators applied successfully');
            })
            .catch(error => {
                console.log('Could not fetch component status data:', error);
                
                // Fallback: apply status based on existing status badges
                const statusBadges = document.querySelectorAll('.component-status');
                statusBadges.forEach(badge => {
                    const component = badge.closest('.component');
                    if (component) {
                        // Try to determine status from badge classes
                        for (let i = 1; i <= 4; i++) {
                            if (badge.classList.contains(`status-${i}`)) {
                                component.setAttribute('data-status', i);
                                component.classList.add(`status-${i}`);
                                console.log(`Applied fallback status ${i}`);
                                break;
                            }
                        }
                    }
                });
            });
    }
    
    // Get human-readable status text
    function getStatusText(status) {
        const statusTexts = {
            1: 'Operational',
            2: 'Performance Issues',
            3: 'Partial Outage', 
            4: 'Major Outage'
        };
        return statusTexts[status] || 'Unknown';
    }
    
    // Initialize when DOM is ready
    function init() {
        console.log('Status indicators script loaded');
        
        // Apply status indicators after a short delay to ensure DOM is ready
        setTimeout(applyStatusIndicators, 1500);
        
        // Reapply status indicators when components are updated
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if new components were added
                    const hasNewComponents = Array.from(mutation.addedNodes).some(node => 
                        node.nodeType === 1 && (
                            node.classList.contains('component') || 
                            node.querySelector('.component')
                        )
                    );
                    
                    if (hasNewComponents) {
                        console.log('New components detected, reapplying status indicators');
                        setTimeout(applyStatusIndicators, 500);
                    }
                }
            });
        });
        
        // Observe changes to the component container
        const componentContainer = document.querySelector('.components') || 
                                 document.querySelector('.component-group') || 
                                 document.body;
        
        if (componentContainer) {
            observer.observe(componentContainer, {
                childList: true,
                subtree: true
            });
            console.log('Mutation observer set up for component changes');
        }
    }
    
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();
