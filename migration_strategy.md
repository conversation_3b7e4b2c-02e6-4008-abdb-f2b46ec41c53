# Database Migration Strategy

## 1. Overview

This document outlines the strategy for migrating the TurdParty database schema from its current state to the target state as defined in the model fixes tasks (T13-T16). We'll use Alembic to manage all migrations in a controlled, reversible manner.

## 2. Migration Approach

We'll adopt a phased migration approach to minimize disruption and risk:

1. **Phase 1**: Structural changes that don't affect existing data
2. **Phase 2**: Data migrations to adapt to new schemas
3. **Phase 3**: Constraint additions after data is properly formatted
4. **Phase 4**: Validation and cleanup

### 2.1 Key Migration Challenges

The following challenges need special attention:

- **Table Renaming**: `vagrant_vm` → `vagrant_vms`, `vm_injection` → `vm_injections`
- **Column Additions**: New columns like `roles`, `mfa_enabled`, etc.
- **Data Type Changes**: Adding length limits and more specific types
- **Relationship Changes**: Fixing relationship definitions and cascade behaviors
- **Constraint Additions**: Adding new constraints after data is verified
- **Default Value Changes**: Updating default values to match new model definitions

## 3. Alembic Migration Plan

### 3.1 Migration Framework Setup

1. First, ensure Alembic is properly configured:
   ```bash
   cd /home/<USER>/dev/10Baht/turdparty
   python migrations/init_alembic.py
   ```

2. Verification:
   ```bash
   alembic current
   ```

### 3.2 Migration Sequence

The migrations will be generated and applied in the following sequence:

#### Migration 1: Base Model Enhancements

```bash
alembic revision -m "enhance_base_model"
```

Implement in the migration:
- Add type annotations
- Convert standard `is_deleted` property to `hybrid_property`
- Add `default_query` class method
- Add global filter for soft-deleted records

#### Migration 2: Table Structure Changes

```bash
alembic revision -m "table_structure_changes"
```

Implement in the migration:
- Rename tables: `vagrant_vm` → `vagrant_vms`, `vm_injection` → `vm_injections`
- Add new indexes on foreign keys and other frequently queried columns
- Create history tracking table `model_history`

#### Migration 3: Column Additions and Type Modifications

```bash
alembic revision -m "column_additions_and_type_mods"
```

Implement in the migration:
- User model:
  - Add `roles` column with default "user"
  - Add MFA-related columns
  - Add login tracking columns
- VagrantVM model:
  - Add resource tracking columns (JSONB columns)
  - Update string column lengths
  - Add `uptime_seconds` column
- VMInjection model:
  - Update string column lengths
  - Add timestamp columns

#### Migration 4: Data Migrations

```bash
alembic revision -m "data_migrations"
```

Implement in the migration:
- Update status values to conform to new enums
- Initialize any new required fields with sensible defaults
- Normalize email addresses (lowercase)
- Update relationship references to match new table names

#### Migration 5: Add Constraints

```bash
alembic revision -m "add_constraints"
```

Implement in the migration:
- Add CHECK constraints for string lengths
- Add CHECK constraints for numeric ranges
- Add proper uniqueness constraints
- Update foreign key constraints to include CASCADE behaviors

## 4. Data Dependencies

The following data dependencies must be carefully managed:

1. **Users → Related Entities**: 
   - All entities with `owner_id` depend on user records
   - Strategy: Use CASCADE for updates, restrict for deletes or use soft deletes

2. **VagrantVM → VM Injections**:
   - VM injections depend on VM records
   - Strategy: Use CASCADE for deletion to maintain referential integrity

3. **File Selections → VM Injections**:
   - VM injections depend on file selection records
   - Strategy: Use CASCADE for deletion

## 5. Rollback Procedures

Each Alembic migration will include a detailed `downgrade()` function to allow for precise rollbacks:

### 5.1 Rolling Back Individual Migrations

```bash
# Roll back the most recent migration
alembic downgrade -1

# Roll back to a specific migration
alembic downgrade <migration_id>

# Roll back all migrations
alembic downgrade base
```

### 5.2 Rollback Procedures by Phase

#### Phase 1 (Structure) Rollback
- Drop added indexes
- Revert table renames
- Remove added columns with no dependencies

#### Phase 2 (Data) Rollback
- Restore original data from backups if available
- For simple transformations, apply inverse transformations

#### Phase 3 (Constraints) Rollback
- Drop added constraints
- Revert any modified default values

### 5.3 Emergency Rollback Procedure

In case of critical failure during migration:

1. Capture error logs and state information
2. Execute immediate rollback: `alembic downgrade <last_known_good_revision>`
3. Restore database from pre-migration backup if rollback fails
4. Switch application back to compatible version

## 6. Validation Steps

Each migration should be validated through the following steps:

### 6.1 Pre-Migration Validation

1. **Schema Verification**: 
   ```sql
   -- Verify current schema before migration
   SELECT table_name, column_name, data_type 
   FROM information_schema.columns 
   WHERE table_schema = 'public'
   ORDER BY table_name, ordinal_position;
   ```

2. **Data Sampling**:
   ```sql
   -- Sample data from key tables
   SELECT * FROM users LIMIT 10;
   SELECT * FROM vagrant_vm LIMIT 10;
   SELECT * FROM vm_injection LIMIT 10;
   ```

3. **Count Records**:
   ```sql
   -- Count records in each table
   SELECT COUNT(*) FROM users;
   SELECT COUNT(*) FROM vagrant_vm;
   SELECT COUNT(*) FROM vm_injection;
   ```

### 6.2 Post-Migration Validation

1. **Schema Verification**:
   ```sql
   -- Verify schema after migration
   SELECT table_name, column_name, data_type 
   FROM information_schema.columns 
   WHERE table_schema = 'public'
   ORDER BY table_name, ordinal_position;
   ```

2. **Constraint Verification**:
   ```sql
   -- Verify constraints
   SELECT tc.table_name, tc.constraint_name, tc.constraint_type
   FROM information_schema.table_constraints tc
   WHERE tc.table_schema = 'public'
   ORDER BY tc.table_name;
   ```

3. **Record Count Validation**:
   ```sql
   -- Ensure no data loss
   SELECT COUNT(*) FROM users;
   SELECT COUNT(*) FROM vagrant_vms;
   SELECT COUNT(*) FROM vm_injections;
   ```

4. **Application Testing**:
   - Run comprehensive test suite
   - Test CRUD operations on all affected entities
   - Verify relationships are working as expected

## 7. Testing Strategy

### 7.1 Development Environment Testing

1. **Database Clone**:
   ```bash
   # Create a development copy of the production database
   pg_dump -h <host> -U <user> -d <prod_db> > prod_dump.sql
   psql -h localhost -U <dev_user> -d <dev_db> < prod_dump.sql
   ```

2. **Migration Dry Run**:
   ```bash
   # Apply migrations with echo
   alembic upgrade head --sql > migration_sql.sql
   
   # Review the generated SQL before execution
   ```

3. **Automated Testing**:
   ```bash
   # Run all tests after migration
   pytest -xvs tests/
   ```

### 7.2 Staging Environment Testing

1. Apply migrations to staging environment
2. Run full application test suite
3. Perform manual testing of key features
4. Verify performance impact with realistic load

## 8. Implementation Plan

### 8.1 Preparation (Pre-Migration)

1. **Database Backup**:
   ```bash
   pg_dump -h <host> -U <user> -d <db_name> > pre_migration_backup.sql
   ```

2. **Schema Snapshot**:
   ```bash
   pg_dump -h <host> -U <user> -d <db_name> --schema-only > pre_migration_schema.sql
   ```

3. **Application Compatibility**:
   - Ensure application code is updated to work with both old and new schemas
   - Implement backward compatibility layers where needed

### 8.2 Execution (Migration Day)

1. **Maintenance Window Announcement**: Schedule and announce downtime
2. **Database Backup**: Final backup before migration
3. **Apply Migrations**: Execute Alembic migrations in sequence
4. **Validation**: Run all validation steps
5. **Application Deployment**: Deploy compatible application version
6. **Monitoring**: Closely monitor application for issues

### 8.3 Post-Migration

1. **Performance Monitoring**: Watch for any performance degradation
2. **Issue Tracking**: Document and address any issues
3. **Clean Up**: Remove any temporary compatibility code

## 9. Conclusion

This migration strategy provides a systematic approach to upgrading the database schema while minimizing risk and disruption. By following this plan, we can successfully transition to the improved data model that addresses all the identified issues in the current implementation.

---

## Appendix A: Migration Script Skeletons

### A.1. Phase 1: Structure Changes

```python
"""enhance_base_model

Revision ID: <auto_generated>
Revises: <previous_revision>
Create Date: <auto_generated>

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '<auto_generated>'
down_revision = '<previous_revision>'
branch_labels = None
depends_on = None

def upgrade():
    # Changes to base model functionality
    # (No direct schema changes required)
    pass

def downgrade():
    # No changes to revert
    pass
```

```python
"""table_structure_changes

Revision ID: <auto_generated>
Revises: <previous_revision>
Create Date: <auto_generated>

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '<auto_generated>'
down_revision = '<previous_revision>'
branch_labels = None
depends_on = None

def upgrade():
    # Rename tables
    op.rename_table('vagrant_vm', 'vagrant_vms')
    op.rename_table('vm_injection', 'vm_injections')
    
    # Create history tracking table
    op.create_table('model_history',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('model', sa.String(), nullable=False),
        sa.Column('record_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('changes', postgresql.JSONB(), nullable=False),
        sa.Column('changed_by_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id'), nullable=True),
        sa.Column('changed_at', sa.DateTime(), nullable=False, default=sa.func.now())
    )
    
    # Add indexes
    op.create_index('ix_vagrant_vms_owner_id', 'vagrant_vms', ['owner_id'])
    op.create_index('ix_vagrant_vms_status', 'vagrant_vms', ['status'])
    op.create_index('ix_vm_injections_vagrant_vm_id', 'vm_injections', ['vagrant_vm_id'])
    op.create_index('ix_vm_injections_owner_id', 'vm_injections', ['owner_id'])
    op.create_index('ix_users_username_email', 'users', ['username', 'email'])

def downgrade():
    # Drop indexes
    op.drop_index('ix_users_username_email', table_name='users')
    op.drop_index('ix_vm_injections_owner_id', table_name='vm_injections')
    op.drop_index('ix_vm_injections_vagrant_vm_id', table_name='vm_injections')
    op.drop_index('ix_vagrant_vms_status', table_name='vagrant_vms')
    op.drop_index('ix_vagrant_vms_owner_id', table_name='vagrant_vms')
    
    # Drop history tracking table
    op.drop_table('model_history')
    
    # Revert table renames
    op.rename_table('vm_injections', 'vm_injection')
    op.rename_table('vagrant_vms', 'vagrant_vm')
```

### A.2. Phase 2: Column Additions and Type Modifications

```python
"""column_additions_and_type_mods

Revision ID: <auto_generated>
Revises: <previous_revision>
Create Date: <auto_generated>

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '<auto_generated>'
down_revision = '<previous_revision>'
branch_labels = None
depends_on = None

def upgrade():
    # User model changes
    op.add_column('users', sa.Column('roles', sa.String(255), nullable=False, server_default='user'))
    op.add_column('users', sa.Column('mfa_enabled', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('users', sa.Column('mfa_secret', sa.String(32), nullable=True))
    op.add_column('users', sa.Column('last_login', sa.DateTime(), nullable=True))
    op.add_column('users', sa.Column('failed_login_attempts', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('users', sa.Column('locked_until', sa.DateTime(), nullable=True))
    
    # Modify column types for User model
    op.alter_column('users', 'username', type_=sa.String(50))
    op.alter_column('users', 'email', type_=sa.String(255))
    op.alter_column('users', 'full_name', type_=sa.String(100))
    op.alter_column('users', 'password_hash', type_=sa.String(255))
    
    # VagrantVM model changes
    op.add_column('vagrant_vms', sa.Column('cpu_usage', postgresql.JSONB(), nullable=True))
    op.add_column('vagrant_vms', sa.Column('memory_usage', postgresql.JSONB(), nullable=True))
    op.add_column('vagrant_vms', sa.Column('disk_usage', postgresql.JSONB(), nullable=True))
    op.add_column('vagrant_vms', sa.Column('uptime_seconds', sa.Integer(), nullable=False, server_default='0'))
    
    # Modify column types for VagrantVM model
    op.alter_column('vagrant_vms', 'name', type_=sa.String(100))
    op.alter_column('vagrant_vms', 'template', type_=sa.String(100))
    op.alter_column('vagrant_vms', 'status', type_=sa.String(20))
    op.alter_column('vagrant_vms', 'ip_address', type_=sa.String(50), nullable=True)
    op.alter_column('vagrant_vms', 'vagrant_id', type_=sa.String(100), nullable=True)
    op.alter_column('vagrant_vms', 'last_action', type_=sa.String(50), nullable=True)
    op.alter_column('vagrant_vms', 'domain', type_=sa.String(100))
    
    # VMInjection model changes
    op.alter_column('vm_injections', 'status', type_=sa.String(20))
    
def downgrade():
    # Revert User model changes
    op.alter_column('users', 'password_hash', type_=sa.String())
    op.alter_column('users', 'full_name', type_=sa.String())
    op.alter_column('users', 'email', type_=sa.String())
    op.alter_column('users', 'username', type_=sa.String())
    
    op.drop_column('users', 'locked_until')
    op.drop_column('users', 'failed_login_attempts')
    op.drop_column('users', 'last_login')
    op.drop_column('users', 'mfa_secret')
    op.drop_column('users', 'mfa_enabled')
    op.drop_column('users', 'roles')
    
    # Revert VagrantVM model changes
    op.alter_column('vagrant_vms', 'domain', type_=sa.String())
    op.alter_column('vagrant_vms', 'last_action', type_=sa.String(), nullable=True)
    op.alter_column('vagrant_vms', 'vagrant_id', type_=sa.String(), nullable=True)
    op.alter_column('vagrant_vms', 'ip_address', type_=sa.String(), nullable=True)
    op.alter_column('vagrant_vms', 'status', type_=sa.String())
    op.alter_column('vagrant_vms', 'template', type_=sa.String())
    op.alter_column('vagrant_vms', 'name', type_=sa.String())
    
    op.drop_column('vagrant_vms', 'uptime_seconds')
    op.drop_column('vagrant_vms', 'disk_usage')
    op.drop_column('vagrant_vms', 'memory_usage')
    op.drop_column('vagrant_vms', 'cpu_usage')
    
    # Revert VMInjection model changes
    op.alter_column('vm_injections', 'status', type_=sa.String())
```

### A.3. Phase 3: Add Constraints

```python
"""add_constraints

Revision ID: <auto_generated>
Revises: <previous_revision>
Create Date: <auto_generated>

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '<auto_generated>'
down_revision = '<previous_revision>'
branch_labels = None
depends_on = None

def upgrade():
    # User model constraints
    op.create_primary_key('pk_users', 'users', ['id'])
    op.create_unique_constraint('uq_users_username', 'users', ['username'])
    op.create_unique_constraint('uq_users_email', 'users', ['email'])
    op.create_check_constraint('ck_username_length', 'users', 'LENGTH(username) BETWEEN 3 AND 50')
    op.create_check_constraint('ck_password_hash_length', 'users', 'LENGTH(password_hash) > 10')
    
    # Modify foreign keys to add CASCADE behavior
    # First, drop existing foreign keys
    op.drop_constraint('vagrant_vms_owner_id_fkey', 'vagrant_vms', type_='foreignkey')
    op.drop_constraint('vm_injections_owner_id_fkey', 'vm_injections', type_='foreignkey')
    op.drop_constraint('vm_injections_vagrant_vm_id_fkey', 'vm_injections', type_='foreignkey')
    op.drop_constraint('vm_injections_file_selection_id_fkey', 'vm_injections', type_='foreignkey')
    
    # Recreate with proper CASCADE behavior
    op.create_foreign_key('vagrant_vms_owner_id_fkey', 'vagrant_vms', 'users', 
                         ['owner_id'], ['id'], ondelete='CASCADE', onupdate='CASCADE')
    op.create_foreign_key('vm_injections_owner_id_fkey', 'vm_injections', 'users', 
                         ['owner_id'], ['id'], ondelete='CASCADE', onupdate='CASCADE')
    op.create_foreign_key('vm_injections_vagrant_vm_id_fkey', 'vm_injections', 'vagrant_vms', 
                         ['vagrant_vm_id'], ['id'], ondelete='CASCADE', onupdate='CASCADE')
    op.create_foreign_key('vm_injections_file_selection_id_fkey', 'vm_injections', 'file_selections', 
                         ['file_selection_id'], ['id'], ondelete='CASCADE', onupdate='CASCADE')
    
    # VagrantVM model constraints
    op.create_primary_key('pk_vagrant_vms', 'vagrant_vms', ['id'])
    op.create_check_constraint('ck_vm_name_length', 'vagrant_vms', 'LENGTH(name) BETWEEN 3 AND 100')
    op.create_check_constraint('ck_cpus_range', 'vagrant_vms', 'cpus BETWEEN 1 AND 16')
    op.create_check_constraint('ck_memory_range', 'vagrant_vms', 'memory_mb BETWEEN 512 AND 32768')
    op.create_check_constraint('ck_disk_range', 'vagrant_vms', 'disk_gb BETWEEN 1 AND 500')
    
    # VMInjection model constraints
    op.create_primary_key('pk_vm_injections', 'vm_injections', ['id'])

def downgrade():
    # Remove VMInjection model constraints
    op.drop_constraint('pk_vm_injections', 'vm_injections', type_='primary')
    
    # Remove VagrantVM model constraints
    op.drop_constraint('ck_disk_range', 'vagrant_vms', type_='check')
    op.drop_constraint('ck_memory_range', 'vagrant_vms', type_='check')
    op.drop_constraint('ck_cpus_range', 'vagrant_vms', type_='check')
    op.drop_constraint('ck_vm_name_length', 'vagrant_vms', type_='check')
    op.drop_constraint('pk_vagrant_vms', 'vagrant_vms', type_='primary')
    
    # Revert foreign key constraints
    op.drop_constraint('vm_injections_file_selection_id_fkey', 'vm_injections', type_='foreignkey')
    op.drop_constraint('vm_injections_vagrant_vm_id_fkey', 'vm_injections', type_='foreignkey')
    op.drop_constraint('vm_injections_owner_id_fkey', 'vm_injections', type_='foreignkey')
    op.drop_constraint('vagrant_vms_owner_id_fkey', 'vagrant_vms', type_='foreignkey')
    
    # Recreate original foreign keys without CASCADE
    op.create_foreign_key('vm_injections_file_selection_id_fkey', 'vm_injections', 'file_selections', 
                         ['file_selection_id'], ['id'])
    op.create_foreign_key('vm_injections_vagrant_vm_id_fkey', 'vm_injections', 'vagrant_vms', 
                         ['vagrant_vm_id'], ['id'])
    op.create_foreign_key('vm_injections_owner_id_fkey', 'vm_injections', 'users', 
                         ['owner_id'], ['id'])
    op.create_foreign_key('vagrant_vms_owner_id_fkey', 'vagrant_vms', 'users', 
                         ['owner_id'], ['id'])
    
    # Remove User model constraints
    op.drop_constraint('ck_password_hash_length', 'users', type_='check')
    op.drop_constraint('ck_username_length', 'users', type_='check')
    op.drop_constraint('uq_users_email', 'users', type_='unique')
    op.drop_constraint('uq_users_username', 'users', type_='unique')
    op.drop_constraint('pk_users', 'users', type_='primary')
```

## Appendix B: Model Changes Summary

### Changes to User Model
- Added type hints
- Added role management functionality
- Added MFA support
- Added account security features
- Improved password handling
- Enhanced validation

### Changes to VagrantVM Model
- Renamed table from vagrant_vm to vagrant_vms
- Added resource tracking
- Added state management
- Added validation
- Improved relationship definitions

### Changes to VMInjection Model
- Renamed table from vm_injection to vm_injections
- Renamed class from Vm_injection to VMInjection
- Added validation
- Improved relationship definitions
- Added status management 