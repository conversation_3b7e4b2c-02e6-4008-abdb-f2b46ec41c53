<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Documentation Test</title>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css">
    <style>
        /* Dark mode styles */
        body {
            background-color: #1a1a1a;
            color: #e0e0e0;
            margin: 0;
        }
        /* Additional styles omitted for brevity */
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js"></script>
    <script>
        window.onload = function() {
            console.log("Loading Swagger UI...");
            const ui = SwaggerUIBundle({
                url: 'http://localhost:3050/api/v1/docs/all/openapi.json',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIBundle.SwaggerUIStandalonePreset
                ],
                layout: "BaseLayout",
                docExpansion: "list",
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                defaultModelRendering: "example",
                displayRequestDuration: true,
                filter: true,
                withCredentials: true,
            });
            console.log("Swagger UI loaded");
        };
    </script>
</body>
</html>
