#!/usr/bin/env python
"""
Fix database script - drops the problematic ix_test_cases_id index.
"""
import os
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import SQLAlchemy
try:
    from sqlalchemy import create_engine, text
except ImportError:
    logger.error("SQLAlchemy is not installed. Please install it with 'pip install sqlalchemy'")
    sys.exit(1)

def fix_database():
    """Fix the database by dropping the problematic index."""
    # Get database URL from environment variable or use default
    database_url = os.environ.get("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/app")
    
    logger.info(f"Connecting to database: {database_url}")
    
    try:
        # Create engine
        engine = create_engine(database_url)
        
        # Connect to database
        with engine.connect() as connection:
            # Drop the problematic index
            logger.info("Dropping index ix_test_cases_id")
            connection.execute(text("DROP INDEX IF EXISTS ix_test_cases_id;"))
            connection.commit()
            
            logger.info("Index dropped successfully")
            
            # Verify the index is gone
            result = connection.execute(text("SELECT indexname FROM pg_indexes WHERE indexname = 'ix_test_cases_id';"))
            if result.fetchone() is None:
                logger.info("Verified index ix_test_cases_id no longer exists")
            else:
                logger.warning("Index ix_test_cases_id still exists. There might be another issue.")
                
        logger.info("Database fix completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error fixing database: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_database()
    sys.exit(0 if success else 1) 