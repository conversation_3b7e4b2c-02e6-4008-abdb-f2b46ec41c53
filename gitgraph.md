```mermaid
gitGraph
    commit id: "e2f29c81" tag: "Enhance UI with FontAwesome icons"

    branch "refactor/friday"
    checkout "refactor/friday"
    commit id: "8701bae8" tag: "Enhance API interface with modern landing page"
    commit id: "2b0dacb3" tag: "Add main.py and run_app.py"
    commit id: "680c8573" tag: "Add tests for UI improvements"
    commit id: "777cd91f" tag: "Increase Playwright test coverage"
    commit id: "6e80faa6" tag: "Add comprehensive quality tests"

    branch "refactor-friday/container-namespace"
    checkout "refactor-friday/container-namespace"
    commit id: "a36a5489" tag: "Namespace all containers"
    commit id: "b33f92db" tag: "Update run-all-tests.sh"

    checkout main
    commit id: "7bd098a1" tag: "Fix database dependencies"

    branch "refactor/endpoint-consolidation"
    checkout "refactor/endpoint-consolidation"
    commit id: "153f1a6e" tag: "Endpoint consolidation"

    checkout main
    commit id: "74352a2f" tag: "Fix file upload functionality"
    commit id: "b640a5b6" tag: "Fix integration tests" tag: "v1.0.0-combined"

    branch "fix-file-upload"
    checkout "fix-file-upload"
    commit id: "562109a4" tag: "Prelucerne"
    commit id: "fcb680c5" tag: "Update branch structure"
    commit id: "edfd0c58" tag: "Add API and health dashboard"

    # Branch feature/sphinx-documentation has been deleted after cherry-picking useful PDF generation improvements

    checkout main
    merge "fix-file-upload" id: "3eeaaed8" tag: "Merge fix-file-upload"
    commit id: "10a1489e" tag: "Add Sphinx documentation framework"
    commit id: "6245ae5c" tag: "Organize documentation"
    commit id: "2f95d131" tag: "Add documentation guides"

    checkout main
    commit id: "c73b9dab" tag: "v1.5.0-sphinx-docs"
    commit id: "8b5bcda2" tag: "v1.6.0-dark-mode-UI"

    branch "feature/enhanced-pdf-generation"
    checkout "feature/enhanced-pdf-generation"
    commit id: "b24c88d7" tag: "Add simple method for PDF generation"
    commit id: "f68bc301" tag: "Update Makefile for PDF generation"

    checkout main
    merge "feature/enhanced-pdf-generation" id: "e728050c" tag: "Merge PDF enhancements"
```
