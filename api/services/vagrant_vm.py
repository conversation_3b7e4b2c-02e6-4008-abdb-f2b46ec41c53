"""
Service for vagrant_vm feature
"""
from fastapi import BackgroundTasks, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import and_
from api.models.vagrant_vm import VagrantVMCreateSchema, VagrantVMUpdateSchema, VagrantVMStatusSchema
from api.db.models.vagrant_vm import VagrantVM as Vagrant_vm  # Alias for backward compatibility
import uuid
import os
import tempfile
import paramiko
import logging
import subprocess
import time
from typing import List, Optional, Tuple, Dict, Any
from datetime import datetime
from api.services.vagrant_client import VagrantClient

logger = logging.getLogger(__name__)

class Vagrant_vmService:
    """Service for vagrant_vm operations"""
    
    def __init__(self, db: Session, vagrant_client: Optional[VagrantClient] = None):
        """Initialize the service.
        
        Args:
            db: Database session
            vagrant_client: Optional VagrantClient instance. If not provided, one will be created when needed.
        """
        self.db = db
        self._vagrant_client = vagrant_client
    
    @property
    def vagrant_client(self) -> VagrantClient:
        """Get or create the VagrantClient instance."""
        if self._vagrant_client is None:
            self._vagrant_client = VagrantClient()
        return self._vagrant_client
    
    def get_all(self, skip: int = 0, limit: int = 100, user_id: uuid.UUID = None, domain: str = None) -> Tuple[List[Vagrant_vm], int]:
        """Get all vagrant_vms with optional filtering by user_id and domain"""
        query = self.db.query(Vagrant_vm)
        if user_id:
            query = query.filter(Vagrant_vm.owner_id == user_id)
        
        # Add domain filtering if specified
        if domain:
            query = query.filter(Vagrant_vm.domain == domain)
        
        total = query.count()
        items = query.offset(skip).limit(limit).all()
        return items, total
    
    def get_by_id(self, vagrant_vm_id: uuid.UUID, user_id: uuid.UUID = None) -> Optional[Vagrant_vm]:
        """Get vagrant_vm by ID with optional user_id check"""
        query = self.db.query(Vagrant_vm).filter(Vagrant_vm.id == vagrant_vm_id)
        if user_id:
            query = query.filter(Vagrant_vm.owner_id == user_id)
        return query.first()
    
    async def create(self, vagrant_vm_data: VagrantVMCreateSchema, background_tasks: BackgroundTasks, user_id: uuid.UUID) -> Vagrant_vm:
        """Create a new vagrant_vm"""
        db_vagrant_vm = Vagrant_vm(
            name=vagrant_vm_data.name,
            description=vagrant_vm_data.description,
            template=vagrant_vm_data.template.value,
            memory_mb=vagrant_vm_data.memory_mb,
            cpus=vagrant_vm_data.cpus,
            disk_gb=vagrant_vm_data.disk_gb,
            owner_id=user_id,
            status="creating",
            domain=vagrant_vm_data.domain or "TurdParty"  # Ensure domain is set, default to TurdParty
        )
        self.db.add(db_vagrant_vm)
        self.db.commit()
        self.db.refresh(db_vagrant_vm)
        
        # Add background task to create the VM
        if vagrant_vm_data.auto_start:
            # Use the new method that creates and starts the VM with Vagrant integration
            background_tasks.add_task(self._create_and_start_vm, db_vagrant_vm.id, vagrant_vm_data)
        else:
            # Just create the VM record without starting it
            background_tasks.add_task(self._create_vm_async, db_vagrant_vm.id)
        
        return db_vagrant_vm
    
    def _create_vm_async(self, vagrant_vm_id: uuid.UUID):
        """Background task to create the VM"""
        # This would contain the actual VM creation logic
        # For now, we'll just update the status
        db_vagrant_vm = self.get_by_id(vagrant_vm_id)
        if db_vagrant_vm:
            db_vagrant_vm.status = "running"
            self.db.commit()
    
    async def _create_and_start_vm(self, vagrant_vm_id: uuid.UUID, vagrant_vm_data: VagrantVMCreateSchema):
        """Background task to create and start the VM with actual Vagrant integration.
        
        Args:
            vagrant_vm_id: The UUID of the VM to create and start.
            vagrant_vm_data: The VM configuration data.
            
        This method:
        1. Gets the VM from the database
        2. Creates Vagrant directory structure
        3. Generates Vagrantfile
        4. Connects to Vagrant client
        5. Starts the VM
        6. Updates VM status
        """
        try:
            # Get VM from database
            db_vagrant_vm = self.get_by_id(vagrant_vm_id)
            if not db_vagrant_vm:
                logger.error(f"VM with ID {vagrant_vm_id} not found in database")
                return
            
            # Create VM directory
            vm_dir = os.path.join(os.environ.get("VAGRANT_WORKSPACE_DIR", "/tmp/vagrant"), str(vagrant_vm_id))
            if not os.path.exists(vm_dir):
                os.makedirs(vm_dir, exist_ok=True)
            
            # Generate Vagrantfile
            self._generate_vagrantfile(vm_dir, vagrant_vm_data, vagrant_vm_id)
            
            # Initialize Vagrant client
            client = self.vagrant_client
            connected = await client.connect()
            if not connected:
                logger.error(f"Failed to connect to Vagrant service for VM {vagrant_vm_id}")
                db_vagrant_vm.status = "error"
                db_vagrant_vm.error_message = "Failed to connect to Vagrant service"
                self.db.commit()
                return
            
            # Start VM
            result = await client.up(str(vagrant_vm_id), provision=True)
            
            if not result.get("success"):
                error_msg = result.get("error", "Unknown error starting VM")
                logger.error(f"Failed to start VM {vagrant_vm_id}: {error_msg}")
                db_vagrant_vm.status = "error"
                db_vagrant_vm.error_message = error_msg
                self.db.commit()
                return
            
            # Update VM status
            db_vagrant_vm.status = "running"
            db_vagrant_vm.last_action = "created and started"
            db_vagrant_vm.last_action_time = datetime.now()
            self.db.commit()
            
            logger.info(f"Successfully created and started VM {vagrant_vm_id}")
            
        except Exception as e:
            logger.exception(f"Error in _create_and_start_vm for {vagrant_vm_id}: {str(e)}")
            try:
                db_vagrant_vm = self.get_by_id(vagrant_vm_id)
                if db_vagrant_vm:
                    db_vagrant_vm.status = "error"
                    db_vagrant_vm.error_message = str(e)
                    self.db.commit()
            except Exception as inner_e:
                logger.exception(f"Error updating VM status after failure: {str(inner_e)}")
    
    def _generate_vagrantfile(self, vm_dir: str, vagrant_vm_data: VagrantVMCreateSchema, vm_id: uuid.UUID) -> str:
        """Generate a Vagrantfile for the VM.
        
        Args:
            vm_dir: The directory where the Vagrantfile will be saved.
            vagrant_vm_data: The VM configuration data.
            vm_id: The VM ID.
            
        Returns:
            The path to the generated Vagrantfile.
        """
        template = vagrant_vm_data.template.value
        memory_mb = vagrant_vm_data.memory_mb
        cpus = vagrant_vm_data.cpus
        
        # Define vagrant box based on template
        if template.startswith("WINDOWS"):
            # Windows vagrant box config
            if template == "WINDOWS_10":
                box = "gusztavvargadr/windows-10"
                communicator = "winrm"
            elif template == "WINDOWS_SERVER_2019":
                box = "gusztavvargadr/windows-server"
                communicator = "winrm"
            else:
                box = "generic/alpine312"  # Default to Alpine as fallback
                communicator = "ssh"
        else:
            # Linux vagrant box config
            if template == "UBUNTU_2004":
                box = "generic/ubuntu2004"  # Using generic box which supports libvirt
            elif template == "DEBIAN_11":
                box = "generic/debian11"
            elif template == "CENTOS_8":
                box = "generic/centos8"
            elif template == "ALPINE_3":
                box = "generic/alpine312"
            else:
                box = "generic/ubuntu2004"  # Default to Ubuntu 20.04 as fallback
            communicator = "ssh"
        
        # Generate Vagrantfile content
        if communicator == "winrm":
            # Windows VM Vagrantfile
            vagrantfile_content = f'''
Vagrant.configure("2") do |config|
  config.vm.box = "{box}"
  config.vm.hostname = "vm-{vm_id}"
  
  # Resource configuration
  config.vm.provider "libvirt" do |libvirt|
    libvirt.memory = {memory_mb}
    libvirt.cpus = {cpus}
    libvirt.graphics_type = "vnc"
    libvirt.video_type = "qxl"
  end
  
  # Windows-specific configuration
  config.vm.communicator = "winrm"
  config.winrm.username = "vagrant"
  config.winrm.password = "vagrant"
  
  # Network configuration
  config.vm.network "private_network", type: "dhcp"
  config.vm.network "forwarded_port", guest: 3389, host: 33389, auto_correct: true
  config.vm.network "forwarded_port", guest: 5985, host: 55985, auto_correct: true
  
  # Provisioning
  config.vm.provision "shell", inline: <<-SHELL
    # Basic Windows setup script
    Set-ExecutionPolicy Bypass -Scope Process -Force
    # Add any additional setup here
  SHELL
end
'''
        else:
            # Linux VM Vagrantfile
            vagrantfile_content = f'''
Vagrant.configure("2") do |config|
  config.vm.box = "{box}"
  config.vm.hostname = "vm-{vm_id}"
  
  # Resource configuration
  config.vm.provider "libvirt" do |libvirt|
    libvirt.memory = {memory_mb}
    libvirt.cpus = {cpus}
    libvirt.graphics_type = "vnc"
    libvirt.video_type = "qxl"
  end
  
  # Network configuration
  config.vm.network "private_network", type: "dhcp"
  config.vm.network "forwarded_port", guest: 22, host: 2222, auto_correct: true
  
  # Provisioning
  config.vm.provision "shell", inline: <<-SHELL
    # Basic Linux setup script
    apt-get update || yum update -y || apk update
    # Add any additional setup here
  SHELL
end
'''
        
        # Write Vagrantfile
        vagrantfile_path = os.path.join(vm_dir, "Vagrantfile")
        with open(vagrantfile_path, "w") as f:
            f.write(vagrantfile_content)
        
        return vagrantfile_path
    
    def update(self, vagrant_vm_id: uuid.UUID, vagrant_vm_data: VagrantVMUpdateSchema, user_id: uuid.UUID = None) -> Vagrant_vm:
        """Update a vagrant_vm"""
        db_vagrant_vm = self.get_by_id(vagrant_vm_id, user_id)
        if not db_vagrant_vm:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Vagrant VM with id {vagrant_vm_id} not found"
            )
            
        # Check if domain is being changed to something other than TurdParty
        if vagrant_vm_data.domain is not None and vagrant_vm_data.domain != "TurdParty":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Domain can only be set to TurdParty"
            )
        
        # Update only provided fields
        update_data = vagrant_vm_data.dict(exclude_unset=True)
        
        for key, value in update_data.items():
            setattr(db_vagrant_vm, key, value)
        
        self.db.commit()
        self.db.refresh(db_vagrant_vm)
        return db_vagrant_vm
    
    async def perform_action(self, vagrant_vm_id: uuid.UUID, action: str, background_tasks: BackgroundTasks, user_id: uuid.UUID = None) -> VagrantVMStatusSchema:
        """Perform an action on a vagrant_vm (start, stop, restart, destroy)"""
        db_vagrant_vm = self.get_by_id(vagrant_vm_id, user_id)
        if not db_vagrant_vm:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Vagrant VM with id {vagrant_vm_id} not found"
            )
        
        # Update status based on action
        if action == "start":
            db_vagrant_vm.status = "starting"
            background_tasks.add_task(self._start_vm_async, vagrant_vm_id)
        elif action == "stop":
            db_vagrant_vm.status = "stopping"
            background_tasks.add_task(self._stop_vm_async, vagrant_vm_id)
        elif action == "restart":
            db_vagrant_vm.status = "restarting"
            background_tasks.add_task(self._restart_vm_async, vagrant_vm_id)
        elif action == "destroy":
            db_vagrant_vm.status = "destroying"
            background_tasks.add_task(self._destroy_vm_async, vagrant_vm_id)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid action: {action}. Must be one of: start, stop, restart, destroy"
            )
        
        self.db.commit()
        return self.get_status(vagrant_vm_id, user_id)
    
    def _start_vm_async(self, vagrant_vm_id: uuid.UUID):
        """Background task to start the VM"""
        # This would contain the actual VM start logic
        # For now, we'll just update the status
        db_vagrant_vm = self.get_by_id(vagrant_vm_id)
        if db_vagrant_vm:
            db_vagrant_vm.status = "running"
            self.db.commit()
    
    def _stop_vm_async(self, vagrant_vm_id: uuid.UUID):
        """Background task to stop the VM"""
        # This would contain the actual VM stop logic
        # For now, we'll just update the status
        db_vagrant_vm = self.get_by_id(vagrant_vm_id)
        if db_vagrant_vm:
            db_vagrant_vm.status = "stopped"
            self.db.commit()
    
    def _restart_vm_async(self, vagrant_vm_id: uuid.UUID):
        """Background task to restart the VM"""
        # This would contain the actual VM restart logic
        # For now, we'll just update the status
        db_vagrant_vm = self.get_by_id(vagrant_vm_id)
        if db_vagrant_vm:
            db_vagrant_vm.status = "running"
            self.db.commit()
    
    def _destroy_vm_async(self, vagrant_vm_id: uuid.UUID):
        """Background task to destroy the VM"""
        # This would contain the actual VM destroy logic
        # For now, we'll just update the status
        db_vagrant_vm = self.get_by_id(vagrant_vm_id)
        if db_vagrant_vm:
            db_vagrant_vm.status = "destroyed"
            self.db.commit()
    
    def _log_command_execution(
        self, 
        vagrant_vm_id: uuid.UUID, 
        command: str, 
        user_id: uuid.UUID, 
        real_execution: bool = True, 
        ssh_host: str = "127.0.0.1", 
        ssh_port: int = 2222,
        ssh_username: str = "vagrant",
        ssh_password: str = "vagrant",
        connection_timeout: int = 10,
        fallback_to_simulation: bool = True,
        fallback_to_vagrant_cli: bool = True
    ) -> Dict[str, Any]:
        """Log the execution of a command on a VM.
        
        Args:
            vagrant_vm_id: The ID of the Vagrant VM.
            command: The command to execute.
            user_id: The ID of the user executing the command.
            real_execution: Whether to actually execute the command on the VM.
            ssh_host: The SSH host to connect to.
            ssh_port: The SSH port to connect to.
            ssh_username: The SSH username to use.
            ssh_password: The SSH password to use.
            connection_timeout: Timeout for SSH connection in seconds.
            fallback_to_simulation: Whether to fall back to simulated execution if SSH connection fails.
            fallback_to_vagrant_cli: Whether to fall back to using the host's Vagrant CLI if SSH connection fails.
            
        Returns:
            A dictionary with the command execution result.
        """
        vm = self.get_by_id(vagrant_vm_id)

        if not vm:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Vagrant VM with ID {vagrant_vm_id} not found",
            )

        result = {
            "success": False,
            "output": "",
            "error": None,
        }

        # Only attempt real execution if requested and VM is running
        if real_execution and vm.status == "running":
            ssh_successful = False
            
            # First attempt: Direct SSH connection
            try:
                logger.info(f"Attempting to execute command on VM {vagrant_vm_id} via SSH: {command}")
                
                import paramiko
                client = paramiko.SSHClient()
                client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                
                try:
                    client.connect(
                        hostname=ssh_host,
                        port=ssh_port,
                        username=ssh_username,
                        password=ssh_password,
                        timeout=connection_timeout,
                        allow_agent=False,
                        look_for_keys=False,
                        banner_timeout=connection_timeout
                    )
                    
                    logger.info(f"Successfully connected to VM {vagrant_vm_id}")
                    stdin, stdout, stderr = client.exec_command(command)
                    
                    # Get command output
                    output = stdout.read().decode('utf-8')
                    error = stderr.read().decode('utf-8')
                    exit_code = stdout.channel.recv_exit_status()
                    
                    logger.info(f"Command executed with exit code {exit_code}")
                    
                    if exit_code == 0:
                        result["success"] = True
                        result["output"] = output
                    else:
                        result["success"] = False
                        result["output"] = output
                        result["error"] = error if error else f"Command failed with exit code {exit_code}"
                    
                    client.close()
                    ssh_successful = True
                    
                except Exception as e:
                    logger.warning(f"SSH connection error: {str(e)}")
                    ssh_error = f"[Errno {e.errno if hasattr(e, 'errno') else None}] {str(e)}"
                    result["error"] = ssh_error
                    
                    # SSH failed, but we'll try other methods below
                    
            except ImportError:
                logger.warning("Paramiko library not installed, skipping direct SSH attempt")
                ssh_error = "Paramiko library not installed"
            
            # Second attempt: Use vagrant CLI on host (if SSH failed and fallback is enabled)
            if not ssh_successful and fallback_to_vagrant_cli:
                try:
                    logger.info(f"Attempting to execute command via Vagrant CLI on host for VM {vm.name}")
                    
                    # Path to the host-side script
                    host_script = "/app/vagrant_exec.sh"
                    
                    if not os.path.isfile(host_script):
                        logger.error(f"Host-side script not found: {host_script}")
                        raise FileNotFoundError(f"Host-side script not found: {host_script}")
                    
                    # Command to execute the host-side script with the VM name and command
                    # We escape the command to make sure it's passed correctly to the script
                    import shlex
                    escaped_command = shlex.quote(command)
                    vagrant_cmd = f"{host_script} {vm.name} {escaped_command}"
                    
                    # Execute command as a subprocess
                    logger.info(f"Executing: {vagrant_cmd}")
                    process = subprocess.Popen(
                        vagrant_cmd,
                        shell=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )
                    
                    # Get output with timeout
                    try:
                        stdout, stderr = process.communicate(timeout=60)  # 60 second timeout
                        exit_code = process.returncode
                        
                        if exit_code == 0:
                            result["success"] = True
                            result["output"] = stdout
                            result["execution_method"] = "vagrant_cli"
                            logger.info("Vagrant CLI execution successful")
                        else:
                            result["success"] = False
                            result["output"] = stdout
                            result["error"] = stderr or f"Command failed with exit code {exit_code}"
                            logger.warning(f"Vagrant CLI execution failed with exit code {exit_code}: {stderr}")
                    
                    except subprocess.TimeoutExpired:
                        process.kill()
                        logger.error("Vagrant CLI execution timed out")
                        result["error"] = "Command execution timed out"
                        
                        # We'll continue to simulation if this fails
                        
                except Exception as e:
                    logger.error(f"Error using Vagrant CLI: {str(e)}")
                    result["error"] = f"Vagrant CLI error: {str(e)}"
                    
                    if not fallback_to_simulation:
                        # Don't fall back to simulation, just return the error from both attempts
                        result["error"] = f"SSH error: {ssh_error if 'ssh_error' in locals() else 'Unknown'}\nVagrant CLI error: {str(e)}"
                        result["success"] = False
                        return result
                
                # If we got a successful result from Vagrant CLI, return it
                if result["success"]:
                    # Log this action to the VM action log
                    action = f"exec_command: {command[:50]}..." if len(command) > 50 else f"exec_command: {command}"
                    vm.last_action = action
                    self.db.commit()
                    
                    return result

        # Fall back to simulated execution if all real execution attempts failed or weren't requested
        if (not real_execution) or (real_execution and not result["success"] and fallback_to_simulation):
            logger.info(f"Simulating command execution on VM {vagrant_vm_id}: {command}")
            
            # Simulated execution is always successful in this mock implementation
            result["success"] = True
            result["output"] = f"[SIMULATED] Command would execute: {command}\n\nThis is simulated output."
            result["execution_method"] = "simulation"
            
            if vm.status != "running":
                result["output"] += f"\n\nNote: VM is not running (status: {vm.status}). In a real environment, the command would not execute."

        # Log this action to the VM action log
        action = f"exec_command: {command[:50]}..." if len(command) > 50 else f"exec_command: {command}"
        vm.last_action = action
        vm.last_action_time = datetime.now()
        self.db.commit()
        
        return result
    
    def execute_command_on_vm(
        self, 
        vagrant_vm_id: uuid.UUID, 
        command: str, 
        user_id: uuid.UUID = None, 
        real_execution: bool = True,
        fallback_to_vagrant_cli: bool = True,
        fallback_to_simulation: bool = True
    ) -> Dict[str, Any]:
        """Execute a command on a VM
        
        This is the public method to be called from the API
        
        Args:
            vagrant_vm_id: The ID of the Vagrant VM
            command: The command to execute
            user_id: The ID of the user executing the command
            real_execution: Whether to attempt real execution via SSH
            fallback_to_vagrant_cli: Whether to fall back to using Vagrant CLI if SSH fails
            fallback_to_simulation: Whether to fall back to simulation if both SSH and Vagrant CLI fail
            
        Returns:
            A dictionary with the command execution result
        """
        db_vagrant_vm = self.get_by_id(vagrant_vm_id, user_id)
        if not db_vagrant_vm:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Vagrant VM with id {vagrant_vm_id} not found"
            )
        
        # Check if VM is running (only if real execution is requested)
        if real_execution and db_vagrant_vm.status != "running":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"VM must be in 'running' state to execute commands (current: {db_vagrant_vm.status})"
            )
        
        # Execute command
        try:
            # Check if we're in test mode
            test_mode = os.environ.get("API_TEST_MODE", "").lower() == "true"
            
            # In test mode, force simulation unless explicitly requested
            if test_mode and real_execution:
                logger.info("Running in test mode, defaulting to simulation")
                real_execution = False
            
            # Execute the command with the specified options
            result = self._log_command_execution(
                vagrant_vm_id, 
                command, 
                user_id, 
                real_execution=real_execution,
                fallback_to_vagrant_cli=fallback_to_vagrant_cli,
                fallback_to_simulation=fallback_to_simulation
            )
            
            # Add execution method to response if it was set
            response = {
                "id": str(vagrant_vm_id),
                "name": db_vagrant_vm.name,
                "status": "executed" if result["success"] else "failed",
                "command": command,
                "output": result["output"],
                "error": result["error"],
                "success": result["success"]
            }
            
            # Include execution method in response if available
            if "execution_method" in result:
                response["execution_method"] = result["execution_method"]
            
            return response
        
        except Exception as e:
            logger.error(f"Error executing command on VM {vagrant_vm_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error executing command: {str(e)}"
            )
    
    def get_status(self, vagrant_vm_id: uuid.UUID, user_id: uuid.UUID = None) -> VagrantVMStatusSchema:
        """Get the status of a vagrant_vm"""
        db_vagrant_vm = self.get_by_id(vagrant_vm_id, user_id)
        if not db_vagrant_vm:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Vagrant VM with id {vagrant_vm_id} not found"
            )
        
        return VagrantVMStatusSchema(
            id=db_vagrant_vm.id,
            name=db_vagrant_vm.name,
            status=db_vagrant_vm.status,
            ip_address=db_vagrant_vm.ip_address,
            ssh_port=db_vagrant_vm.ssh_port,
            last_action=db_vagrant_vm.last_action,
            last_action_time=db_vagrant_vm.last_action_time,
            error_message=db_vagrant_vm.error_message
        )
    
    async def delete(self, vagrant_vm_id: uuid.UUID, background_tasks: BackgroundTasks, user_id: uuid.UUID = None) -> None:
        """Delete a vagrant_vm"""
        db_vagrant_vm = self.get_by_id(vagrant_vm_id, user_id)
        if not db_vagrant_vm:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Vagrant VM with id {vagrant_vm_id} not found"
            )
        
        # First destroy the VM if it exists and is not already destroyed
        if db_vagrant_vm.status != "destroyed":
            await self.perform_action(vagrant_vm_id, "destroy", background_tasks, user_id)
        
        # Then delete the DB record
        self.db.delete(db_vagrant_vm)
        self.db.commit()
