"""
User-related Pydantic schemas for API validation.
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field, validator
import re

from api.schemas.base import BaseSchema, ResponseBase


class UserBase(BaseSchema):
    """Base model for user data."""
    username: str = Field(..., min_length=3, max_length=50, pattern=r"^[a-zA-Z0-9_]+$", 
                          description="Username (3-50 characters, alphanumeric and underscores only)")
    email: EmailStr = Field(..., description="Email address")
    full_name: str = Field(..., min_length=1, max_length=100, 
                           description="User's full name")
    is_active: bool = Field(True, description="Whether the user account is active")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "username": "johndoe",
                "email": "<EMAIL>",
                "full_name": "<PERSON>",
                "is_active": True
            }
        }
    }


class UserCreate(UserBase):
    """Model for creating a new user."""
    password: str = Field(..., min_length=8, max_length=100, 
                         description="Password (min 8 characters)")
    
    @validator("password")
    def password_complexity(cls, value: str) -> str:
        """Validate password complexity."""
        if len(value) < 8:
            raise ValueError("Password must be at least 8 characters")
        
        if not re.search(r"[A-Z]", value):
            raise ValueError("Password must contain at least one uppercase letter")
        
        if not re.search(r"[a-z]", value):
            raise ValueError("Password must contain at least one lowercase letter")
        
        if not re.search(r"[0-9]", value):
            raise ValueError("Password must contain at least one digit")
        
        if not re.search(r"[^a-zA-Z0-9]", value):
            raise ValueError("Password must contain at least one special character")
        
        return value


class UserUpdate(BaseSchema):
    """Model for updating user data."""
    full_name: Optional[str] = Field(None, min_length=1, max_length=100, 
                                    description="User's full name")
    email: Optional[EmailStr] = Field(None, description="Email address")
    is_active: Optional[bool] = Field(None, description="Whether the user account is active")
    password: Optional[str] = Field(None, min_length=8, max_length=100, 
                                   description="New password (min 8 characters)")
    
    @validator("password")
    def password_complexity(cls, value: Optional[str]) -> Optional[str]:
        """Validate password complexity if provided."""
        if value is None:
            return None
            
        if len(value) < 8:
            raise ValueError("Password must be at least 8 characters")
        
        if not re.search(r"[A-Z]", value):
            raise ValueError("Password must contain at least one uppercase letter")
        
        if not re.search(r"[a-z]", value):
            raise ValueError("Password must contain at least one lowercase letter")
        
        if not re.search(r"[0-9]", value):
            raise ValueError("Password must contain at least one digit")
        
        if not re.search(r"[^a-zA-Z0-9]", value):
            raise ValueError("Password must contain at least one special character")
        
        return value


class UserRole(BaseSchema):
    """Model for user role operations."""
    role: str = Field(..., description="Role name")


class UserResponse(ResponseBase, UserBase):
    """Model for user response."""
    is_superuser: bool = Field(False, description="Whether the user has superuser privileges")
    last_login: Optional[datetime] = Field(None, description="Timestamp of last login")
    mfa_enabled: bool = Field(False, description="Whether MFA is enabled for this user")
    roles: List[str] = Field([], description="List of roles assigned to the user")


class UserLoginRequest(BaseSchema):
    """Model for user login requests."""
    username: str = Field(..., description="Username or email")
    password: str = Field(..., description="Password")
    mfa_code: Optional[str] = Field(None, description="MFA code if enabled")


class UserLoginResponse(BaseSchema):
    """Model for user login responses."""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field("bearer", description="Token type")
    user: UserResponse = Field(..., description="User data")
    mfa_required: bool = Field(False, description="Whether MFA verification is required")

# For backward compatibility
User = UserResponse
UserInDB = UserResponse
