"""
Tests for the consolidated Vagrant VM endpoints.
"""
import pytest
import uuid
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from api.application import get_application
from api.core.test_config import test_settings
from api.schemas.user import UserSchema

# Enable test mode
test_settings.set_testing(True)

# Create a mock user for testing
@pytest.fixture
def mock_user():
    """Create a mock user for testing."""
    return UserSchema(
        id=uuid.uuid4(),
        email="<EMAIL>",
        username="testuser",
        is_active=True,
        is_superuser=False
    )

# Mock the database session
@pytest.fixture
def mock_db():
    """Create a mock database session."""
    mock = MagicMock()
    with patch("api.db.session.get_db", return_value=mock):
        yield mock

# Mock the Vagrant client
@pytest.fixture
def mock_vagrant_client():
    """Create a mock Vagrant client."""
    mock = MagicMock()
    # Mock connection status
    mock.connect.return_value = True
    # Mock list_boxes
    mock.list_boxes.return_value = [{"name": "ubuntu/focal64", "provider": "virtualbox", "version": "1.0.0"}]
    # Mock VM status
    mock.get_machine_status.return_value = {"status": "running"}
    # Mock VM info
    mock.get_machine_info.return_value = {"name": "test-vm", "provider": "virtualbox", "state": "running"}

    with patch("api.routes.consolidated_vagrant.VagrantClient", return_value=mock):
        with patch("api.routes.consolidated_vagrant.get_vagrant_client", return_value=mock):
            yield mock

# Mock the VM injection service
@pytest.fixture
def mock_vm_injection_service(mock_db):
    """Create a mock VM injection service."""
    mock = MagicMock()
    # Mock get_all
    mock.get_all.return_value = ([], 0)

    with patch("api.routes.consolidated_vagrant.VMInjectionService", return_value=mock):
        yield mock

# Mock the Vagrant VM service
@pytest.fixture
def mock_vagrant_vm_service(mock_db):
    """Create a mock Vagrant VM service."""
    mock = MagicMock()
    # Mock get_all
    mock.get_all.return_value = ([], 0)

    with patch("api.routes.consolidated_vagrant.VagrantVMService", return_value=mock):
        yield mock

# Mock the get_current_user dependency
@pytest.fixture
def mock_auth(mock_user):
    """Mock the authentication dependency."""
    with patch("api.middleware.auth_middleware.get_current_user", return_value=mock_user):
        yield

@pytest.fixture
def client(mock_auth, mock_db, mock_vagrant_client, mock_vm_injection_service, mock_vagrant_vm_service):
    """Create a test client for the application with all mocks."""
    app = get_application()
    with TestClient(app) as client:
        yield client

def test_connection_status(client):
    """Test the connection status endpoint."""
    response = client.get("/api/v1/virtual-machines/connection")
    assert response.status_code == 200
    data = response.json()
    assert "connected" in data

def test_list_vms(client):
    """Test the list VMs endpoint."""
    response = client.get("/api/v1/virtual-machines")
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "total" in data

def test_vm_status_not_found(client, mock_vagrant_client):
    """Test the VM status endpoint with a non-existent VM."""
    # Mock a 404 response
    mock_vagrant_client.get_machine_status.return_value = {"error": "VM not found"}

    response = client.get("/api/v1/virtual-machines/00000000-0000-0000-0000-000000000000/status")
    assert response.status_code == 404

def test_vm_info_not_found(client, mock_vagrant_client):
    """Test the VM info endpoint with a non-existent VM."""
    # Mock a 404 response
    mock_vagrant_client.get_machine_info.return_value = {"error": "VM not found"}

    response = client.get("/api/v1/virtual-machines/non-existent-vm/info")
    assert response.status_code == 404

def test_list_boxes(client):
    """Test the list boxes endpoint."""
    response = client.get("/api/v1/virtual-machines/boxes")
    assert response.status_code == 200

def test_list_injections(client):
    """Test the list injections endpoint."""
    response = client.get("/api/v1/virtual-machines/injections")
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "total" in data

def test_vm_templates(client):
    """Test the VM templates endpoint."""
    response = client.get("/api/v1/virtual-machines/templates")
    assert response.status_code == 200

def test_vm_action_not_found(client, mock_vagrant_vm_service):
    """Test the VM action endpoint with a non-existent VM."""
    # Mock a 404 response
    mock_vagrant_vm_service.get_by_id.return_value = None

    response = client.post("/api/v1/virtual-machines/00000000-0000-0000-0000-000000000000/start")
    assert response.status_code == 404

def test_vm_execute_not_found(client, mock_vagrant_client):
    """Test the VM execute endpoint with a non-existent VM."""
    # Mock a 404 response
    mock_vagrant_client.execute_command.return_value = {"error": "VM not found"}

    response = client.post("/api/v1/virtual-machines/00000000-0000-0000-0000-000000000000/execute",
                          json={"command": "echo hello"})
    assert response.status_code == 404

def test_vm_injection_status_not_found(client, mock_vm_injection_service):
    """Test the VM injection status endpoint with a non-existent injection."""
    # Mock a 404 response
    mock_vm_injection_service.get_by_id.return_value = None

    response = client.get("/api/v1/virtual-machines/injections/00000000-0000-0000-0000-000000000000/status")
    assert response.status_code == 404

def test_backward_compatibility_vagrant(client):
    """Test backward compatibility with original vagrant endpoints."""
    # Test path endpoint
    response = client.get("/api/v1/vagrant/connection/status")
    assert response.status_code == 307  # Redirect

    # Follow redirects
    response = client.get("/api/v1/vagrant/connection/status", follow_redirects=True)
    assert response.status_code == 200
    data = response.json()
    assert "connected" in data

    # Test root endpoint
    response = client.get("/api/v1/vagrant")
    assert response.status_code == 307  # Redirect

    # Follow redirects
    response = client.get("/api/v1/vagrant", follow_redirects=True)
    assert response.status_code == 200

    # Test POST to root endpoint
    response = client.post("/api/v1/vagrant")
    assert response.status_code == 307  # Redirect

def test_backward_compatibility_vagrant_vm(client):
    """Test backward compatibility with original vagrant_vm endpoints."""
    # Test path endpoint
    response = client.get("/api/v1/vagrant_vm/templates")
    assert response.status_code == 307  # Redirect

    # Follow redirects
    response = client.get("/api/v1/vagrant_vm/templates", follow_redirects=True)
    assert response.status_code == 200

    # Test root endpoint
    response = client.get("/api/v1/vagrant_vm")
    assert response.status_code == 307  # Redirect

    # Follow redirects
    response = client.get("/api/v1/vagrant_vm", follow_redirects=True)
    assert response.status_code == 200

    # Test POST to root endpoint
    response = client.post("/api/v1/vagrant_vm")
    assert response.status_code == 307  # Redirect

def test_backward_compatibility_vm_injection(client):
    """Test backward compatibility with original vm_injection endpoints."""
    # Test root endpoint
    response = client.get("/api/v1/vm_injection")
    assert response.status_code == 307  # Redirect

    # Follow redirects
    response = client.get("/api/v1/vm_injection", follow_redirects=True)
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert "total" in data

    # Test POST to root endpoint
    response = client.post("/api/v1/vm_injection")
    assert response.status_code == 307  # Redirect

    # Test path endpoint
    response = client.get("/api/v1/vm_injection/status")
    assert response.status_code == 307  # Redirect

def test_backward_compatibility_vagrant_double_prefix(client):
    """Test backward compatibility with double-prefixed vagrant endpoints."""
    response = client.get("/api/v1/vagrant/vagrant/status")
    assert response.status_code == 307  # Redirect

    # Follow redirects
    response = client.get("/api/v1/vagrant/vagrant/status", follow_redirects=True)
    assert response.status_code == 200

def test_backward_compatibility_dash_endpoints(client):
    """Test backward compatibility with dash versions of endpoints."""
    response = client.get("/api/v1/vagrant-vm")
    assert response.status_code == 307  # Redirect

    # Follow redirects
    response = client.get("/api/v1/vagrant-vm", follow_redirects=True)
    assert response.status_code == 200

    # Test vm-injection endpoint
    response = client.get("/api/v1/vm-injection")
    assert response.status_code == 307  # Redirect

    # Follow redirects
    response = client.get("/api/v1/vm-injection", follow_redirects=True)
    assert response.status_code == 200

def test_backward_compatibility_vm_injection(client):
    """Test backward compatibility with original vm_injection endpoints."""
    response = client.get("/api/v1/vm_injection")
    assert response.status_code == 200

def test_alias_compatibility_vms(client):
    """Test compatibility with vms alias endpoints."""
    response = client.get("/api/v1/vms/connection")
    assert response.status_code == 200

def test_double_prefix_compatibility(client):
    """Test compatibility with double-prefixed vagrant endpoints."""
    response = client.get("/api/v1/vagrant/vagrant/connection/status")
    assert response.status_code == 200
