"""
Tests for vagrant_vm.py service using mocks to improve coverage.

This test module focuses on testing the Vagrant VM service with mocks
to avoid external dependencies and improve test coverage.
"""
import pytest
import uuid
from unittest.mock import MagicMock, patch, AsyncMock
from datetime import datetime

from api.services.vagrant_vm import Vagrant_vmService
from api.db.models.vagrant_vm import VagrantVM
from api.schemas.vagrant_vm import (
    VagrantVMCreate,
    VagrantVMUpdate,
    VagrantVMStatus,
    VagrantVMActionRequest
)

# Mark all tests in this module as not requiring database and skip DB setup
pytestmark = [
    pytest.mark.no_db_required,
    pytest.mark.skip_db_setup,
    pytest.mark.unit
]


@pytest.fixture
def mock_db_session():
    """Create a mock database session."""
    mock_session = MagicMock()
    mock_session.add.return_value = None
    mock_session.commit.return_value = None
    mock_session.refresh.return_value = None
    mock_session.query.return_value = mock_session
    mock_session.filter.return_value = mock_session
    mock_session.all.return_value = []
    mock_session.first.return_value = None
    return mock_session


@pytest.fixture
def mock_vagrant_client():
    """Create a mock Vagrant client."""
    mock_client = MagicMock()
    mock_client.up.return_value = {"status": "success"}
    mock_client.halt.return_value = {"status": "success"}
    mock_client.destroy.return_value = {"status": "success"}
    mock_client.status.return_value = {"status": "running"}
    mock_client.provision.return_value = {"status": "success"}
    mock_client.reload.return_value = {"status": "success"}
    mock_client.ssh.return_value = {"status": "success", "output": "test output"}
    return mock_client


@pytest.fixture
def mock_vagrant_vm():
    """Create a mock VagrantVM record."""
    return VagrantVM(
        id=str(uuid.uuid4()),
        name="test-vm",
        box="ubuntu/focal64",
        memory=1024,
        cpus=2,
        status="running",
        provider="virtualbox",
        user_id="test-user",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def vagrant_vm_create_data():
    """Create test data for VagrantVMCreate."""
    return VagrantVMCreate(
        name="test-vm",
        box="ubuntu/focal64",
        memory=1024,
        cpus=2,
        provider="virtualbox"
    )


@pytest.fixture
def vagrant_vm_update_data():
    """Create test data for VagrantVMUpdate."""
    return VagrantVMUpdate(
        name="updated-vm",
        memory=2048,
        cpus=4
    )


@patch("api.services.vagrant_vm.get_vagrant_client")
def test_vagrant_vm_service_init(mock_get_vagrant_client, mock_vagrant_client):
    """Test VagrantVMService initialization."""
    mock_get_vagrant_client.return_value = mock_vagrant_client

    service = VagrantVMService()
    assert service.vagrant_client == mock_vagrant_client


@patch("api.services.vagrant_vm.get_vagrant_client")
def test_get_vagrant_vm_service(mock_get_vagrant_client, mock_vagrant_client):
    """Test get_vagrant_vm_service function."""
    mock_get_vagrant_client.return_value = mock_vagrant_client

    service = get_vagrant_vm_service()
    assert isinstance(service, VagrantVMService)
    assert service.vagrant_client == mock_vagrant_client


@patch("api.services.vagrant_vm.get_vagrant_client")
async def test_create_vagrant_vm(mock_get_vagrant_client, mock_vagrant_client, mock_db_session, vagrant_vm_create_data, mock_vagrant_vm):
    """Test create_vagrant_vm function."""
    mock_get_vagrant_client.return_value = mock_vagrant_client
    mock_db_session.add.return_value = None
    mock_db_session.commit.return_value = None
    mock_db_session.refresh.side_effect = lambda x: setattr(x, 'id', str(uuid.uuid4()))

    # Mock the VagrantVM model
    with patch("api.services.vagrant_vm.VagrantVM") as mock_vagrant_vm_model:
        mock_vagrant_vm_model.return_value = mock_vagrant_vm

        # Call the create_vagrant_vm function
        result = await create_vagrant_vm(
            vagrant_vm=vagrant_vm_create_data,
            user_id="test-user",
            db=mock_db_session
        )

        # Verify the result
        assert result.name == mock_vagrant_vm.name
        assert result.box == mock_vagrant_vm.box
        assert result.memory == mock_vagrant_vm.memory
        assert result.cpus == mock_vagrant_vm.cpus
        assert result.provider == mock_vagrant_vm.provider
        assert result.user_id == mock_vagrant_vm.user_id

        # Verify that the db session methods were called
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()


@patch("api.services.vagrant_vm.get_vagrant_client")
async def test_update_vagrant_vm(mock_get_vagrant_client, mock_vagrant_client, mock_db_session, vagrant_vm_update_data, mock_vagrant_vm):
    """Test update_vagrant_vm function."""
    mock_get_vagrant_client.return_value = mock_vagrant_client
    mock_db_session.commit.return_value = None

    # Call the update_vagrant_vm function
    result = await update_vagrant_vm(
        vagrant_vm=mock_vagrant_vm,
        vagrant_vm_update=vagrant_vm_update_data,
        db=mock_db_session
    )

    # Verify the result
    assert result.name == vagrant_vm_update_data.name
    assert result.memory == vagrant_vm_update_data.memory
    assert result.cpus == vagrant_vm_update_data.cpus

    # Verify that the db session methods were called
    mock_db_session.commit.assert_called_once()


@patch("api.services.vagrant_vm.get_vagrant_client")
async def test_delete_vagrant_vm(mock_get_vagrant_client, mock_vagrant_client, mock_db_session, mock_vagrant_vm):
    """Test delete_vagrant_vm function."""
    mock_get_vagrant_client.return_value = mock_vagrant_client
    mock_db_session.delete.return_value = None
    mock_db_session.commit.return_value = None

    # Call the delete_vagrant_vm function
    await delete_vagrant_vm(
        vagrant_vm=mock_vagrant_vm,
        db=mock_db_session
    )

    # Verify that the vagrant_client.destroy was called
    mock_vagrant_client.destroy.assert_called_once_with(mock_vagrant_vm.name)

    # Verify that the db session methods were called
    mock_db_session.delete.assert_called_once_with(mock_vagrant_vm)
    mock_db_session.commit.assert_called_once()


@patch("api.services.vagrant_vm.get_vagrant_client")
async def test_start_vagrant_vm(mock_get_vagrant_client, mock_vagrant_client, mock_db_session, mock_vagrant_vm):
    """Test start_vagrant_vm function."""
    mock_get_vagrant_client.return_value = mock_vagrant_client
    mock_db_session.commit.return_value = None

    # Call the start_vagrant_vm function
    result = await start_vagrant_vm(
        vagrant_vm=mock_vagrant_vm,
        db=mock_db_session
    )

    # Verify the result
    assert result.status == "running"

    # Verify that the vagrant_client.up was called
    mock_vagrant_client.up.assert_called_once_with(mock_vagrant_vm.name)

    # Verify that the db session methods were called
    mock_db_session.commit.assert_called_once()


@patch("api.services.vagrant_vm.get_vagrant_client")
async def test_stop_vagrant_vm(mock_get_vagrant_client, mock_vagrant_client, mock_db_session, mock_vagrant_vm):
    """Test stop_vagrant_vm function."""
    mock_get_vagrant_client.return_value = mock_vagrant_client
    mock_db_session.commit.return_value = None

    # Call the stop_vagrant_vm function
    result = await stop_vagrant_vm(
        vagrant_vm=mock_vagrant_vm,
        db=mock_db_session
    )

    # Verify the result
    assert result.status == "stopped"

    # Verify that the vagrant_client.halt was called
    mock_vagrant_client.halt.assert_called_once_with(mock_vagrant_vm.name)

    # Verify that the db session methods were called
    mock_db_session.commit.assert_called_once()


@patch("api.services.vagrant_vm.get_vagrant_client")
async def test_restart_vagrant_vm(mock_get_vagrant_client, mock_vagrant_client, mock_db_session, mock_vagrant_vm):
    """Test restart_vagrant_vm function."""
    mock_get_vagrant_client.return_value = mock_vagrant_client
    mock_db_session.commit.return_value = None

    # Call the restart_vagrant_vm function
    result = await restart_vagrant_vm(
        vagrant_vm=mock_vagrant_vm,
        db=mock_db_session
    )

    # Verify the result
    assert result.status == "running"

    # Verify that the vagrant_client.reload was called
    mock_vagrant_client.reload.assert_called_once_with(mock_vagrant_vm.name)

    # Verify that the db session methods were called
    mock_db_session.commit.assert_called_once()
