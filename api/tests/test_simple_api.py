"""Simple API tests that don't require database setup."""

import pytest
from fastapi.testclient import TestClient
from api.application import get_application

@pytest.fixture
def client():
    """Create a test client."""
    app = get_application()
    return TestClient(app)

@pytest.mark.no_db_required
def test_api_health(client):
    """Test the API health endpoint."""
    response = client.get("/api/v1/health/")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
