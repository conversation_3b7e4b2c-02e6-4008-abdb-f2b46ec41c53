"""
Test to verify that authentication is bypassed when test mode is enabled.
"""
import pytest
from fastapi.testclient import Test<PERSON>lient

from api.application import get_application
from api.core.test_config import test_settings

@pytest.fixture
def app_with_test_mode_enabled():
    """Create an application with test mode enabled."""
    # Enable test mode
    test_settings.enable_test_mode()
    # Create application
    app = get_application()
    yield app
    # Disable test mode to clean up
    test_settings.disable_test_mode()

@pytest.fixture
def app_with_test_mode_disabled():
    """Create an application with test mode disabled."""
    # Disable test mode
    test_settings.disable_test_mode()
    # Create application
    app = get_application()
    yield app
    # Re-enable test mode to avoid affecting other tests
    test_settings.enable_test_mode()

@pytest.fixture
def client_with_test_mode_enabled(app_with_test_mode_enabled):
    """Create a test client with test mode enabled."""
    return TestClient(app_with_test_mode_enabled)

@pytest.fixture
def client_with_test_mode_disabled(app_with_test_mode_disabled):
    """Create a test client with test mode disabled."""
    return TestClient(app_with_test_mode_disabled)

def test_auth_bypass(client_with_test_mode_enabled):
    """Test that authentication is bypassed when test mode is enabled."""
    # Try to access a protected endpoint without authentication
    # This should succeed because test mode is enabled
    response = client_with_test_mode_enabled.get("/api/users/me")
    
    # Check that the request was not rejected due to missing authentication
    assert response.status_code != 401, "Authentication was not bypassed"
    
    # Note: The actual status code might be 404 or something else depending on the endpoint
    # The important thing is that it's not 401 (Unauthorized)

def test_auth_required(client_with_test_mode_disabled):
    """Test that authentication is required when test mode is disabled."""
    # Try to access a protected endpoint without authentication
    # This should fail because test mode is disabled
    response = client_with_test_mode_disabled.get("/api/users/me")
    
    # Check that the request was rejected due to missing authentication
    assert response.status_code == 401, "Authentication was not required" 