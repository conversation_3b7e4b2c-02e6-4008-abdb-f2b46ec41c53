#!/usr/bin/env python
"""
Health check routes.
"""
import os
import sys
import logging
import datetime
import glob
from typing import Any, Dict, Optional, List

from fastapi import APIRouter, Depends, Query, status, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from api.core.config import settings
from api.db.session import get_db
from api.middleware.auth_middleware import get_current_user
from api.services.minio_ssh_client import MinIOSSHClient #Added import

logger = logging.getLogger(__name__)
router = APIRouter(tags=["health"])

@router.get("/status", response_model=dict)
async def health_check():
    """
    Check the health of the API service.
    
    Returns:
        dict: Health status information
    """
    try:
        logger.info("Health check request received")
        return {
            "status": "ok",
            "message": "Service is running",
            "timestamp": "",  # Current time would be added here
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.get("/detailed", status_code=status.HTTP_200_OK)
async def detailed_health_check(
    include_system: bool = Query(False, description="Include detailed system information"),
    include_services: bool = Query(True, description="Include service status details"),
    include_coverage: bool = Query(False, description="Include test coverage details"),
    current_user: Optional[Dict[str, Any]] = Depends(get_current_user)
) -> Dict[Any, Any]:
    """
    Detailed health check providing comprehensive status information.

    Args:
        include_system: Whether to include detailed system information
        include_services: Whether to include detailed service status
        include_coverage: Whether to include test coverage details
        current_user: The current authenticated user

    Returns:
        Dict: Comprehensive health status information
    """
    try:
        result = {
            "status": "healthy",
            "version": settings.API_VERSION,
            "environment": settings.ENVIRONMENT,
            "timestamp": datetime.datetime.now().isoformat(),
        }

        # Basic system info
        result["system"] = {
            "python_version": ".".join(map(str, sys.version_info[:3])),
            "hostname": os.environ.get("HOSTNAME", "unknown"),
        }

        # Include detailed system info if requested
        if include_system:
            try:
                import psutil
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')

                result["system"]["detailed"] = {
                    "cpu_count": os.cpu_count(),
                    "memory": {
                        "total_gb": round(memory.total / (1024**3), 2),
                        "available_gb": round(memory.available / (1024**3), 2),
                        "percent_used": memory.percent,
                    },
                    "disk": {
                        "total_gb": round(disk.total / (1024**3), 2),
                        "free_gb": round(disk.free / (1024**3), 2),
                        "percent_used": disk.percent,
                    }
                }
            except ImportError:
                result["system"]["detailed"] = {"error": "psutil not available"}

        # Include service status if requested
        if include_services:
            result["services"] = {
                "database": await check_database_health(),
                # Add more service checks here as needed
                "minio": await check_minio_health()
            }

        # Include test coverage if requested
        if include_coverage:
            try:
                import json
                coverage_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'coverage.json')

                if os.path.exists(coverage_file):
                    with open(coverage_file, 'r') as f:
                        coverage_data = json.load(f)

                    result["test_coverage"] = {
                        "total_percentage": coverage_data.get("totals", {}).get("percent_covered", 0),
                        "last_updated": datetime.datetime.fromtimestamp(
                            os.path.getmtime(coverage_file)
                        ).isoformat(),
                    }
                else:
                    result["test_coverage"] = {"error": "Coverage data not available"}
            except Exception as e:
                logger.error(f"Error getting coverage data: {str(e)}")
                result["test_coverage"] = {"error": f"Error retrieving coverage data: {str(e)}"}

        return result
    except Exception as e:
        logger.error(f"Error in detailed health check: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.datetime.now().isoformat()
        }

@router.get("/ping", response_model=dict)
async def ping():
    """
    Simple ping endpoint for basic connectivity test.
    
    Returns:
        dict: Ping response
    """
    logger.info("Ping request received")
    return {"ping": "pong"}

@router.get("/services/{service_name}", status_code=status.HTTP_200_OK)
async def service_health(service_name: str) -> Dict[str, Any]:
    """
    Check health of a specific service.

    Args:
        service_name: Name of the service to check

    Returns:
        Dict[str, Any]: Service health information
    """
    start_time = datetime.datetime.now()

    if service_name == "database":
        health_data = await check_database_health()
    elif service_name == "minio":
        health_data = await check_minio_health()
    else:
        return {
            "status": "error",
            "error": f"Unknown service: {service_name}",
            "available_services": ["database", "minio"],
            "timestamp": datetime.datetime.now().isoformat()
        }

    response_time = (datetime.datetime.now() - start_time).total_seconds()

    return {
        "service": service_name,
        "status": health_data.get("status", "unknown"),
        "response_time_seconds": response_time,
        "details": health_data,
        "timestamp": datetime.datetime.now().isoformat()
    }

async def check_database_health() -> Dict[str, Any]:
    """
    Check the health of the database.

    Returns:
        Dict[str, Any]: Database health information
    """
    try:
        from api.db.session import async_engine

        # Try to get connection
        conn = await async_engine.connect()
        await conn.execute("SELECT 1")
        await conn.close()

        return {
            "status": "healthy",
            "message": "Database connection successful"
        }
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        return {
            "status": "error",
            "message": f"Database connection failed: {str(e)}"
        }

async def check_minio_health(hostname: str = None) -> Dict[str, Any]:
    """
    Check the health of the MinIO service.

    Args:
        hostname: Optional hostname for the MinIO server

    Returns:
        Dict[str, Any]: MinIO health information
    """
    try:
        # Import here to avoid circular imports
        from api.services.minio_ssh_client import MinIOSSHClient

        client = MinIOSSHClient(hostname=hostname)
        await client.start_ssh_tunnel()

        buckets_info = await client.list_buckets()

        if buckets_info.get("success", False):
            return {
                "status": "healthy",
                "message": "MinIO server is operational",
                "hostname": hostname or "default",
                "buckets_count": len(buckets_info.get("buckets", []))
            }
        else:
            return {
                "status": "error",
                "message": "Failed to list MinIO buckets",
                "hostname": hostname or "default",
                "error": buckets_info.get("error", "Unknown error")
            }
    except Exception as e:
        logger.error(f"MinIO health check failed: {str(e)}")
        return {
            "status": "error",
            "message": f"MinIO connection failed: {str(e)}",
            "hostname": hostname or "default"
        }

@router.get("/system-status", status_code=status.HTTP_200_OK)
async def system_status(
    hostname: str = Query(None, description="Remote server hostname (defaults to environment variable)"),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Comprehensive system status check for all major components.
    
    This endpoint checks:
    - Remote server connectivity
    - MinIO storage service
    - Vagrant gRPC service
    - Database connectivity
    - API health
    
    Args:
        hostname: The hostname of the remote server (optional)
        
    Returns:
        Dict: Status information for all system components
    """
    logger.info("System status check requested")
    
    # Use environment variable for hostname if not provided
    if not hostname:
        hostname = os.getenv("MINIO_SSH_HOST", "localhost")
    
    # Initialize response
    response = {
        "status": "ok",
        "timestamp": datetime.datetime.now().isoformat(),
        "components": {
            "api": {
                "status": "ok",
                "version": settings.API_VERSION,
                "environment": settings.ENVIRONMENT
            },
            "database": {
                "status": "unknown"
            },
            "remote_server": {
                "status": "unknown",
                "hostname": hostname
            },
            "minio": {
                "status": "unknown",
                "hostname": hostname
            },
            "vagrant": {
                "status": "unknown"
            }
        }
    }
    
    # Check database
    try:
        await db.execute("SELECT 1")
        response["components"]["database"]["status"] = "ok"
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        response["components"]["database"]["status"] = "error"
        response["components"]["database"]["error"] = str(e)
    
    # Check remote server connectivity
    ssh_client = None
    try:
        from api.services.minio_ssh_client import MinIOSSHClient
        ssh_client = MinIOSSHClient()
        tunnel_success = await ssh_client.start_ssh_tunnel(hostname)
        
        if tunnel_success:
            response["components"]["remote_server"]["status"] = "ok"
            
            # Check MinIO since we have a tunnel
            try:
                s3_client = ssh_client.get_s3_client()
                buckets_response = s3_client.list_buckets()
                
                bucket_count = len(buckets_response.get('Buckets', []))
                response["components"]["minio"]["status"] = "ok"
                response["components"]["minio"]["bucket_count"] = bucket_count
                response["components"]["minio"]["buckets"] = [
                    {"name": b.get('Name'), "created": b.get('CreationDate').isoformat() if b.get('CreationDate') else None}
                    for b in buckets_response.get('Buckets', [])
                ]
            except Exception as e:
                logger.error(f"MinIO health check failed: {str(e)}")
                response["components"]["minio"]["status"] = "error"
                response["components"]["minio"]["error"] = str(e)
        else:
            response["components"]["remote_server"]["status"] = "error"
            response["components"]["remote_server"]["error"] = "Failed to establish SSH tunnel"
            response["components"]["minio"]["status"] = "error"
            response["components"]["minio"]["error"] = "Cannot connect to MinIO due to SSH tunnel failure"
    except Exception as e:
        logger.error(f"Remote server health check failed: {str(e)}")
        response["components"]["remote_server"]["status"] = "error"
        response["components"]["remote_server"]["error"] = str(e)
    finally:
        if ssh_client:
            await ssh_client.stop_ssh_tunnel()
    
    # Check Vagrant gRPC service
    try:
        from api.services.vagrant_client import VagrantClient
        vagrant_client = VagrantClient()
        connected = await vagrant_client.connect()
        
        if connected:
            response["components"]["vagrant"]["status"] = "ok"
            
            # Get additional info if connected
            try:
                vm_list = await vagrant_client.list_vms()
                response["components"]["vagrant"]["vm_count"] = len(vm_list.get("vms", []))
                response["components"]["vagrant"]["vms"] = vm_list.get("vms", [])
            except Exception as e:
                logger.warning(f"Failed to get VM list: {str(e)}")
        else:
            response["components"]["vagrant"]["status"] = "error"
            response["components"]["vagrant"]["error"] = "Failed to connect to Vagrant service"
        
        await vagrant_client.close()
    except Exception as e:
        logger.error(f"Vagrant service health check failed: {str(e)}")
        response["components"]["vagrant"]["status"] = "error"
        response["components"]["vagrant"]["error"] = str(e)
    
    # Determine overall status
    component_statuses = [comp["status"] for comp in response["components"].values()]
    if "error" in component_statuses:
        response["status"] = "degraded"
    
    return response

@router.get("/test-runs", status_code=status.HTTP_200_OK)
async def test_runs(
    include_history: bool = Query(False, description="Include historical test runs"),
    max_history: int = Query(5, description="Maximum number of historical test runs to include"),
    current_user: Optional[Dict[str, Any]] = Depends(get_current_user)
) -> Dict[Any, Any]:
    """
    Get compilation of the latest test run results and coverage.
    
    Args:
        include_history: Whether to include historical test runs
        max_history: Maximum number of historical test runs to include
        current_user: The current authenticated user
        
    Returns:
        Dict: Test run results and coverage information
    """
    try:
        result = {
            "timestamp": datetime.datetime.now().isoformat(),
            "status": "ok"
        }
        
        # Find all coverage report files
        coverage_files = []
        for pattern in [
            "coverage.json",
            "test-results/coverage/coverage_*.json",
            "htmlcov/coverage.json"
        ]:
            coverage_files.extend(glob.glob(pattern))
        
        # Find most recent coverage file
        latest_coverage = None
        latest_timestamp = 0
        
        for file_path in coverage_files:
            file_mtime = os.path.getmtime(file_path)
            if file_mtime > latest_timestamp:
                latest_timestamp = file_mtime
                latest_coverage = file_path
        
        # Include latest coverage data
        if latest_coverage:
            try:
                import json
                with open(latest_coverage, 'r') as f:
                    coverage_data = json.load(f)
                
                # Extract key information
                totals = coverage_data.get("totals", {})
                result["latest_coverage"] = {
                    "timestamp": datetime.datetime.fromtimestamp(latest_timestamp).isoformat(),
                    "source": os.path.basename(latest_coverage),
                    "total_percentage": totals.get("percent_covered", 0),
                    "num_statements": totals.get("num_statements", 0),
                    "covered_lines": totals.get("covered_lines", 0),
                    "missing_lines": totals.get("missing_lines", 0)
                }
                
                # Add module level summaries
                result["latest_coverage"]["modules"] = []
                for file_path, file_data in coverage_data.get("files", {}).items():
                    summary = file_data.get("summary", {})
                    if summary and "percent_covered" in summary:
                        result["latest_coverage"]["modules"].append({
                            "file": file_path,
                            "percentage": summary.get("percent_covered", 0),
                            "statements": summary.get("num_statements", 0),
                            "missing_lines": summary.get("missing_lines", 0)
                        })
                
                # Sort modules by coverage percentage (ascending)
                result["latest_coverage"]["modules"].sort(key=lambda m: m["percentage"])
                
                # Add "needs improvement" section
                threshold = 70
                result["latest_coverage"]["needs_improvement"] = [
                    m for m in result["latest_coverage"]["modules"] 
                    if m["percentage"] < threshold
                ]
            except Exception as e:
                logger.error(f"Error processing coverage data: {str(e)}")
                result["latest_coverage"] = {"error": f"Failed to process coverage data: {str(e)}"}
        else:
            result["latest_coverage"] = {"error": "No coverage data found"}
        
        # Include test results if available
        test_results_dir = "test-results"
        if os.path.exists(test_results_dir):
            result["test_results"] = {"available": True}
            
            # Find test result files
            test_files = glob.glob(os.path.join(test_results_dir, "*_results.txt"))
            result["test_results"]["files"] = [os.path.basename(f) for f in test_files]
            
            # Parse latest results
            if test_files:
                latest_results = {}
                for test_file in test_files:
                    test_type = os.path.basename(test_file).replace("_results.txt", "")
                    try:
                        with open(test_file, 'r') as f:
                            content = f.read()
                            # Extract basic pass/fail info
                            passed = "passing" in content.lower()
                            failed = "failing" in content.lower() or "failed" in content.lower()
                            latest_results[test_type] = {
                                "status": "passed" if passed and not failed else "failed" if failed else "unknown",
                                "timestamp": datetime.datetime.fromtimestamp(os.path.getmtime(test_file)).isoformat()
                            }
                    except Exception as e:
                        latest_results[test_type] = {"error": str(e)}
                        
                result["test_results"]["summary"] = latest_results
        else:
            result["test_results"] = {"available": False}
            
        # Include historical data if requested
        if include_history:
            history = []
            historical_files = []
            
            # Find all historical coverage files
            for pattern in [
                "test-results/coverage/coverage_*.json"
            ]:
                historical_files.extend(glob.glob(pattern))
                
            # Sort by modification time (newest first)
            historical_files.sort(key=lambda f: os.path.getmtime(f), reverse=True)
            
            # Process files (limit to max_history)
            for file_path in historical_files[:max_history]:
                try:
                    with open(file_path, 'r') as f:
                        coverage_data = json.load(f)
                    
                    timestamp = datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
                    totals = coverage_data.get("totals", {})
                    history.append({
                        "timestamp": timestamp,
                        "file": os.path.basename(file_path),
                        "total_percentage": totals.get("percent_covered", 0),
                        "num_statements": totals.get("num_statements", 0)
                    })
                except Exception as e:
                    logger.error(f"Error processing historical data {file_path}: {str(e)}")
            
            result["history"] = history
            
        return result
    except Exception as e:
        logger.error(f"Error in test runs endpoint: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.datetime.now().isoformat()
        }