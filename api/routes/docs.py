"""
API endpoints for documentation.
"""
import os
import glob
from typing import List, Dict, Any
from datetime import datetime
try:
    import markdown
    MARKDOWN_AVAILABLE = True
except ImportError:
    MARKDOWN_AVAILABLE = False
from fastapi import APIRouter, HTTPException, status, Query
from fastapi.responses import HTMLResponse

router = APIRouter(tags=["documentation"])

@router.get("/", include_in_schema=False)
async def docs_root():
    """
    Redirect to the Swagger UI documentation with virtual-machines section expanded.
    """
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/api/v1/docs/all/docs#/virtual-machines")

def get_markdown_files(search_term: str = None) -> List[Dict[str, Any]]:
    """
    Get all markdown files in the project.

    Args:
        search_term (str, optional): Term to filter markdown files by name or content

    Returns:
        List[Dict[str, Any]]: List of markdown files with metadata
    """
    # Base directory for the project
    base_dir = "/app"  # This is the path in the Docker container

    # Find all markdown files
    md_files = []
    for filepath in glob.glob(f"{base_dir}/**/*.md", recursive=True):
        # Skip files in .git directory
        if "/.git/" in filepath:
            continue

        # Get file stats
        stat = os.stat(filepath)
        modified_time = datetime.fromtimestamp(stat.st_mtime)

        # Get relative path
        rel_path = os.path.relpath(filepath, base_dir)

        # If search term is provided, check if it's in the filename or content
        if search_term:
            # Check if search term is in the filename
            if search_term.lower() not in os.path.basename(filepath).lower():
                # Check if search term is in the content
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    if search_term.lower() not in content.lower():
                        continue  # Skip this file if search term not found
                except Exception:
                    continue  # Skip this file if can't read content

        md_files.append({
            "path": rel_path,
            "modified": modified_time,
            "size": stat.st_size
        })

    # Sort by modified time (newest first)
    md_files.sort(key=lambda x: x["modified"], reverse=True)

    # If feature_development_guide.md exists, move it to the top
    for i, file_info in enumerate(md_files):
        if os.path.basename(file_info["path"]) == "feature_development_guide.md":
            feature_guide = md_files.pop(i)
            md_files.insert(0, feature_guide)
            break

    return md_files

def render_markdown_file(file_path: str) -> str:
    """
    Render a markdown file to HTML.

    Args:
        file_path (str): Path to the markdown file

    Returns:
        str: HTML content
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        if not MARKDOWN_AVAILABLE:
            # Simple HTML rendering if markdown module is not available
            html = f"<pre>{content}</pre>"
            return html

        # Convert markdown to HTML with extensions for mermaid
        html = markdown.markdown(
            content,
            extensions=[
                'markdown.extensions.tables',
                'markdown.extensions.fenced_code',
                'markdown.extensions.codehilite',
                'markdown.extensions.toc'
            ]
        )

        # Add mermaid support - handle both code blocks and triple backtick blocks
        html = html.replace('<code class="language-mermaid">',
                           '<div class="mermaid">')
        html = html.replace('<pre><code class="mermaid">',
                           '<div class="mermaid">')

        # Close the mermaid divs properly
        html = html.replace('</code></pre>', '</div>')
        html = html.replace('</code>', '</div>')

        # Generate table of contents if the document has headings
        toc_html = ""
        if "<h1>" in html or "<h2>" in html or "<h3>" in html:
            toc_html = '<div class="toc"><h2>Table of Contents</h2><ul>'

            # Extract headings and create TOC
            import re
            headings = re.findall(r'<h([1-3]).*?id="(.*?)".*?>(.*?)</h\1>', html)

            for level, id_attr, title in headings:
                indent = (int(level) - 1) * 20
                toc_html += f'<li style="margin-left: {indent}px;"><a href="#{id_attr}">{title}</a></li>'

            toc_html += '</ul></div><hr>'

        # Add the TOC at the beginning of the document
        html = toc_html + html

        return html
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error rendering markdown: {str(e)}"
        )

@router.get("/all", response_class=HTMLResponse)
async def list_all_docs(search: str = Query(None, description="Search term to filter markdown files")):
    """
    List all markdown files in the project.

    Args:
        search (str, optional): Search term to filter markdown files

    Returns:
        HTMLResponse: HTML page with list of markdown files
    """
    md_files = get_markdown_files(search)

    # Create HTML content
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>TurdParty Documentation</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }}
            h1 {{
                border-bottom: 2px solid #eaecef;
                padding-bottom: 10px;
            }}
            .file-list {{
                list-style-type: none;
                padding: 0;
            }}
            .file-item {{
                margin: 10px 0;
                padding: 10px;
                border: 1px solid #e1e4e8;
                border-radius: 6px;
                background-color: #f6f8fa;
            }}
            .file-item:hover {{
                background-color: #f0f3f6;
            }}
            .file-link {{
                font-weight: bold;
                text-decoration: none;
                color: #0366d6;
            }}
            .file-meta {{
                color: #586069;
                font-size: 0.9em;
                margin-top: 5px;
            }}
            .file-preview {{
                margin-top: 15px;
                padding: 15px;
                border: 1px solid #e1e4e8;
                border-radius: 6px;
                background-color: white;
            }}
            .search-container {{
                margin-bottom: 20px;
            }}
            .search-input {{
                padding: 8px 12px;
                width: 300px;
                border: 1px solid #e1e4e8;
                border-radius: 6px;
                font-size: 16px;
            }}
            .search-button {{
                padding: 8px 16px;
                background-color: #0366d6;
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
            }}
            .search-button:hover {{
                background-color: #0256b3;
            }}
            .toc {{
                background-color: #f6f8fa;
                padding: 15px;
                border-radius: 6px;
                margin-bottom: 20px;
            }}
            .toc h2 {{
                margin-top: 0;
            }}
            .toc ul {{
                padding-left: 0;
                list-style-type: none;
            }}
            .toc a {{
                text-decoration: none;
                color: #0366d6;
            }}
            .toc a:hover {{
                text-decoration: underline;
            }}
            /* Dark mode support */
            @media (prefers-color-scheme: dark) {{
                body {{
                    background-color: #0d1117;
                    color: #c9d1d9;
                }}
                h1 {{
                    border-bottom-color: #30363d;
                }}
                .file-item {{
                    border-color: #30363d;
                    background-color: #161b22;
                }}
                .file-item:hover {{
                    background-color: #1c2128;
                }}
                .file-link {{
                    color: #58a6ff;
                }}
                .file-meta {{
                    color: #8b949e;
                }}
                .file-preview {{
                    border-color: #30363d;
                    background-color: #0d1117;
                }}
                .search-input {{
                    background-color: #0d1117;
                    border-color: #30363d;
                    color: #c9d1d9;
                }}
                .search-button {{
                    background-color: #1f6feb;
                }}
                .search-button:hover {{
                    background-color: #388bfd;
                }}
                .toc {{
                    background-color: #161b22;
                }}
                .toc a {{
                    color: #58a6ff;
                }}
            }}
        </style>
        <!-- Include Mermaid for diagram rendering -->
        <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {{
                mermaid.initialize({{
                    startOnLoad: true,
                    theme: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'default',
                    securityLevel: 'loose',
                    flowchart: {{ useMaxWidth: true, htmlLabels: true }}
                }});
            }});
        </script>
    </head>
    <body>
        <h1>TurdParty Documentation</h1>
        <p>This page lists all markdown documentation files in the project, with the most recently modified files at the top.</p>

        <div class="search-container">
            <form action="/api/docs/all" method="get">
                <input type="text" name="search" placeholder="Search documentation..." class="search-input" value="{search or ''}">
                <button type="submit" class="search-button">Search</button>
                {('<a href="/api/docs/all" style="margin-left: 10px;">Clear</a>' if search else '')}
            </form>
        </div>

        <p>{len(md_files)} markdown files found {(f'for search term "{search}"' if search else '')}</p>

        <ul class="file-list">
    """

    # Add each file to the HTML
    for file_info in md_files:
        file_path = f"/app/{file_info['path']}"
        file_name = os.path.basename(file_info['path'])
        modified_str = file_info['modified'].strftime('%Y-%m-%d %H:%M:%S')
        size_kb = file_info['size'] / 1024

        # Try to render the markdown preview
        try:
            html_preview = render_markdown_file(file_path)
        except Exception:
            html_preview = "<p>Error rendering preview</p>"

        html_content += f"""
            <li class="file-item">
                <a href="/api/docs/view/{file_info['path']}" class="file-link">{file_name}</a>
                <div class="file-meta">
                    <span>Path: {file_info['path']}</span> |
                    <span>Modified: {modified_str}</span> |
                    <span>Size: {size_kb:.1f} KB</span>
                </div>
                <div class="file-preview">
                    {html_preview}
                </div>
            </li>
        """

    html_content += """
        </ul>
    </body>
    </html>
    """

    return HTMLResponse(content=html_content)

@router.get("/view/{file_path:path}", response_class=HTMLResponse)
async def view_doc(file_path: str):
    """
    View a specific markdown file.

    Args:
        file_path (str): Path to the markdown file

    Returns:
        HTMLResponse: HTML page with rendered markdown
    """
    full_path = f"/app/{file_path}"

    if not os.path.exists(full_path) or not full_path.endswith('.md'):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found or not a markdown file"
        )

    try:
        html_content = render_markdown_file(full_path)

        # Wrap in HTML document
        html_document = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{os.path.basename(file_path)} - TurdParty Documentation</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 1000px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                pre {{
                    background-color: #f6f8fa;
                    border-radius: 6px;
                    padding: 16px;
                    overflow: auto;
                }}
                code {{
                    font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
                    font-size: 85%;
                }}
                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin-bottom: 16px;
                }}
                th, td {{
                    border: 1px solid #dfe2e5;
                    padding: 6px 13px;
                }}
                th {{
                    background-color: #f6f8fa;
                }}
                img {{
                    max-width: 100%;
                }}
                .back-link {{
                    display: inline-block;
                    margin-bottom: 20px;
                    color: #0366d6;
                    text-decoration: none;
                }}
                .toc {{
                    background-color: #f6f8fa;
                    padding: 15px;
                    border-radius: 6px;
                    margin-bottom: 20px;
                }}
                .toc h2 {{
                    margin-top: 0;
                }}
                .toc ul {{
                    padding-left: 0;
                    list-style-type: none;
                }}
                .toc a {{
                    text-decoration: none;
                    color: #0366d6;
                }}
                .toc a:hover {{
                    text-decoration: underline;
                }}
                .mermaid {{
                    margin: 20px 0;
                    overflow-x: auto;
                }}
                /* Dark mode support */
                @media (prefers-color-scheme: dark) {{
                    body {{
                        background-color: #0d1117;
                        color: #c9d1d9;
                    }}
                    pre {{
                        background-color: #161b22;
                    }}
                    code {{
                        color: #c9d1d9;
                    }}
                    table {{
                        border-color: #30363d;
                    }}
                    th, td {{
                        border-color: #30363d;
                    }}
                    th {{
                        background-color: #161b22;
                    }}
                    .back-link {{
                        color: #58a6ff;
                    }}
                    .toc {{
                        background-color: #161b22;
                    }}
                    .toc a {{
                        color: #58a6ff;
                    }}
                }}
            </style>
            <!-- Include Mermaid for diagram rendering -->
            <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
            <script>
                document.addEventListener('DOMContentLoaded', function() {{
                    mermaid.initialize({{
                        startOnLoad: true,
                        theme: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'default',
                        securityLevel: 'loose',
                        flowchart: {{ useMaxWidth: true, htmlLabels: true }}
                    }});
                }});
            </script>
        </head>
        <body>
            <a href="/api/docs/all" class="back-link">← Back to Documentation List</a>
            <h1>{os.path.basename(file_path)}</h1>
            {html_content}
        </body>
        </html>
        """

        return HTMLResponse(content=html_document)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error rendering markdown: {str(e)}"
        )