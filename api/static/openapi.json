{"openapi": "3.0.3", "info": {"title": "TurdParty API", "description": "API for the TurdParty application with versioned endpoints.", "version": "1.0.0"}, "paths": {"/api/v1/health": {"get": {"summary": "Health Check", "operationId": "health_check", "tags": ["health"], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}}}}}}}}}, "/api/v1/virtual-machines": {"get": {"summary": "List Virtual Machines", "operationId": "list_virtual_machines", "tags": ["virtual-machines"], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "string"}}}}}}}}}}}}