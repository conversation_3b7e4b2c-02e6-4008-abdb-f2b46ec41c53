from sqlalchemy import Column, String, Text, Boolean, ForeignKey, Index, CheckConstraint, PrimaryKeyConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, validates
import re
from typing import Optional, List, Dict, Any

from api.db.base_model import BaseModel, ValidationMixin, HistoryMixin
from api.db.types import ItemStatus

class Item(BaseModel, ValidationMixin, HistoryMixin):
    """Item model for storing item data."""
    __tablename__ = "items"
    
    __table_args__ = (
        PrimaryKeyConstraint('id', name='pk_items'),
        CheckConstraint('LENGTH(title) BETWEEN 3 AND 255', name='ck_title_length'),
        Index('ix_items_owner_id', 'owner_id')
    )
    
    # Item properties
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    status = Column(String(20), default=ItemStatus.ACTIVE.value, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Ownership
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE", onupdate="CASCADE"), 
                      nullable=False)
    
    # Relationships
    owner = relationship("User", back_populates="items", lazy="joined")
    
    # Validation
    @validates('title')
    def validate_title(self, key: str, value: str) -> str:
        """Validate item title."""
        if not value:
            raise ValueError("Title cannot be empty")
        
        if len(value) < 3:
            raise ValueError("Title must be at least 3 characters")
        
        return value
    
    @validates('status')
    def validate_status(self, key: str, value: str) -> str:
        """Validate item status."""
        try:
            return ItemStatus(value).value
        except ValueError:
            valid_statuses = [s.value for s in ItemStatus]
            raise ValueError(f"Invalid status. Must be one of: {', '.join(valid_statuses)}")
    
    def __repr__(self) -> str:
        """Return string representation of the item."""
        return f"<Item(id={self.id}, title={self.title}, status={self.status})>"
    
    # Owner-related methods
    def is_owned_by(self, user_id: str) -> bool:
        """Check if the item is owned by the specified user."""
        return str(self.owner_id) == str(user_id)
    
    # Status tracking methods
    def activate(self) -> None:
        """Activate the item."""
        self.status = ItemStatus.ACTIVE.value
        self.is_active = True
    
    def deactivate(self) -> None:
        """Deactivate the item."""
        self.status = ItemStatus.INACTIVE.value
        self.is_active = False
    
    def archive(self) -> None:
        """Archive the item."""
        self.status = ItemStatus.ARCHIVED.value
        self.is_active = False
