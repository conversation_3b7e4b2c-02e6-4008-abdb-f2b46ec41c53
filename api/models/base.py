"""Base model module."""
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy import Column, DateTime, func
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from api.db.base import Base


class BaseModel(Base):
    """Base model for all database models with common fields and methods."""
    __abstract__ = True
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    @declared_attr
    def __tablename__(cls) -> str:
        """Generate __tablename__ automatically from class name."""
        return cls.__name__.lower()
    
    def soft_delete(self) -> None:
        """Mark the record as soft-deleted."""
        self.deleted_at = datetime.utcnow()
        
    @property
    def is_deleted(self) -> bool:
        """Check if the record is soft-deleted."""
        return self.deleted_at is not None
        
    def restore(self) -> None:
        """Restore a soft-deleted record."""
        self.deleted_at = None
