
"""Item model module."""
from sqlalchemy import Column, String, Text, ForeignKey, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid

from api.models.base import BaseModel


class Item(BaseModel):
    """Item model for the database."""
    
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)
    
    # Relationships
    owner = relationship("User", back_populates="items")
    
    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<Item {self.name}>"
