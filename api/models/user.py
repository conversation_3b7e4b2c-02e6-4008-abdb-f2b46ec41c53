"""User model module."""
from sqlalchemy import <PERSON><PERSON><PERSON>, String, <PERSON><PERSON>an
from sqlalchemy.orm import relationship
from pydantic import BaseModel, EmailStr
from typing import Optional, List
import uuid

from api.models.base import BaseModel as SQLBaseModel


class User(SQLBaseModel):
    """User model for the database."""

    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)

    # MFA fields
    mfa_secret = Column(String(255), nullable=True)
    mfa_enabled = Column(Boolean, default=False)

    # Relationships
    items = relationship("Item", back_populates="owner", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<User {self.email}>"


class UserSchema(BaseModel):
    """User schema for API responses."""
    id: uuid.UUID
    email: str
    full_name: Optional[str] = None
    is_active: bool = True
    is_superuser: bool = False
    mfa_enabled: bool = False

    class Config:
        """Pydantic config."""
        orm_mode = True