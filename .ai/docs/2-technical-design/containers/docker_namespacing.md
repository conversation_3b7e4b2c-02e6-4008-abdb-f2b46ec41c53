# Docker Container Namespacing

## Overview

TurdParty uses a consistent Docker container namespacing convention to improve organization and avoid conflicts with other projects. This document describes the namespacing convention and how it's implemented.

## Namespacing Convention

All Docker containers in the TurdParty project follow a consistent naming convention:

```
turdparty_<service_name>
```

For example:
- `turdparty_api` - The API service
- `turdparty_db` - The PostgreSQL database service
- `turdparty_redis` - The Redis service
- `turdparty_minio` - The MinIO object storage service
- `turdparty_celery_file_ops` - The Celery worker for file operations
- `turdparty_celery_vm_ops` - The Celery worker for VM operations
- `turdparty_celery_monitoring` - The Celery worker for monitoring operations
- `turdparty_test_runner` - The test runner service

## Network Namespacing

Docker networks are also namespaced:

```
turdparty_<environment>_network
```

For example:
- `turdparty_test_network` - The network for the test environment
- `turdparty_dev_network` - The network for the development environment
- `turdparty_prod_network` - The network for the production environment

## Implementation

### Docker Compose Configuration

The Docker Compose files have been updated to use the new naming convention:

```yaml
services:
  db:
    container_name: turdparty_db
    # ...

  redis:
    container_name: turdparty_redis
    # ...

  minio:
    container_name: turdparty_minio
    # ...
```

The network configuration has also been updated:

```yaml
networks:
  default:
    name: turdparty_test_network
    external: false
```

### Scripts and Tools

All scripts and tools that interact with Docker containers have been updated to use the new naming convention:

#### run_single_test.sh

```bash
# Check if the test environment is running
if ! docker compose -f docker-compose.test.yml ps | grep -q "turdparty_test_runner"; then
    # Start the test environment
    # ...
fi

# Run the test
docker compose -f docker-compose.test.yml exec -e IN_DOCKER=true test-runner pytest "$1" -v
```

#### run_celery_tests.sh

```bash
# Start the test environment
docker compose -f docker-compose.test.yml up -d

# Run the Celery integration tests
docker compose -f docker-compose.test.yml exec -e IN_DOCKER=true test-runner pytest tests/integration/test_celery_integration.py -v
```

## Benefits

The namespacing convention provides several benefits:

1. **Clarity**: Container names clearly indicate which project they belong to
2. **Conflict Avoidance**: Prevents conflicts with other Docker projects running on the same host
3. **Organization**: Makes it easier to identify and manage containers
4. **Consistency**: Provides a consistent naming scheme across all environments

## Interacting with Containers

When interacting with containers, use the new naming convention:

```bash
# Check container logs
docker logs turdparty_api

# Execute a command in a container
docker exec -it turdparty_db psql -U turdparty -d turdparty

# Stop a container
docker stop turdparty_api
```

## Troubleshooting

If you encounter issues with the Docker containers:

1. Check the container logs:
   ```bash
   docker logs turdparty_api
   ```

2. Restart the containers:
   ```bash
   cd .dockerwrapper
   docker compose -f docker-compose.test.yml down
   docker compose -f docker-compose.test.yml up -d
   ```

3. Check the container status:
   ```bash
   docker ps -a --filter "name=turdparty_"
   ```

4. Check the network status:
   ```bash
   docker network ls --filter "name=turdparty_"
   ```
