# TurdParty Application Architecture

## Overview

TurdParty is a web application with a microservices architecture consisting of the following main components:

1. **Frontend (React)**: A React-based user interface running on port 3000
2. **Backend API (FastAPI)**: A Python FastAPI application running on port 8000
3. **Database (PostgreSQL)**: A PostgreSQL database running on port 5430
4. **Storage Service (MinIO)**: For file storage and management
5. **Task Processing (Celery)**: For asynchronous task processing
6. **Message Broker (Redis)**: For task queuing and result storage

## Component Relationships

### Frontend (React)

- **Port**: 3000
- **Technology**: React, TypeScript
- **Container**: frontend
- **Dependencies**: Backend API

The frontend is a React application that communicates with the backend API to fetch and display data. It handles user interactions and provides a responsive interface for the application.

### Backend API (FastAPI)

- **Port**: 8000
- **Technology**: Python, FastAPI
- **Container**: turdparty_api
- **Dependencies**: PostgreSQL Database, Redis, MinIO

The backend API is built with FastAPI and provides RESTful endpoints for the frontend to interact with. It handles business logic, authentication, database operations, and task creation.

### Database (PostgreSQL)

- **Port**: 5430
- **Technology**: PostgreSQL 14
- **Container**: turdparty_db
- **Dependencies**: None

The PostgreSQL database stores all application data, including user information, file metadata, VM information, task status, and other application-specific data.

### Storage Service (MinIO)

- **Port**: 9000
- **Technology**: MinIO
- **Container**: turdparty_minio
- **Dependencies**: None

MinIO provides object storage for files uploaded to the application. It's compatible with the Amazon S3 API and provides features like versioning, encryption, and access control.

### Task Processing (Celery)

- **Technology**: Celery
- **Containers**:
  - turdparty_celery_file_ops
  - turdparty_celery_vm_ops
  - turdparty_celery_monitoring
- **Dependencies**: Redis, PostgreSQL Database

Celery handles asynchronous task processing for operations like file uploads, VM management, and monitoring. Tasks are distributed across specialized workers for different types of operations.

### Message Broker (Redis)

- **Port**: 6379
- **Technology**: Redis
- **Container**: turdparty_redis
- **Dependencies**: None

Redis serves as both the message broker for Celery tasks and the result backend for storing task results.

## Communication Flow

1. **User → Frontend**: Users interact with the React frontend through their web browser.
2. **Frontend → Backend API**: The frontend makes HTTP requests to the backend API endpoints.
3. **Backend API → Database**: The backend API queries the PostgreSQL database to retrieve or store data.
4. **Backend API → MinIO**: The backend API stores and retrieves files from MinIO.
5. **Backend API → Redis**: The backend API enqueues tasks in Redis for Celery workers.
6. **Celery Workers → Redis**: Celery workers pick up tasks from Redis queues.
7. **Celery Workers → Database**: Celery workers update task status in the database.
8. **Celery Workers → MinIO**: Celery workers store and retrieve files from MinIO.
9. **Backend API → Frontend**: The backend API returns responses to the frontend.

## Docker Configuration

The application is containerized using Docker with the following containers:

1. **turdparty_api**: Contains the FastAPI backend application
2. **turdparty_frontend**: Contains the React frontend application
3. **turdparty_db**: Contains the PostgreSQL database
4. **turdparty_redis**: Contains the Redis message broker
5. **turdparty_minio**: Contains the MinIO object storage
6. **turdparty_celery_file_ops**: Contains the Celery worker for file operations
7. **turdparty_celery_vm_ops**: Contains the Celery worker for VM operations
8. **turdparty_celery_monitoring**: Contains the Celery worker for monitoring operations
9. **turdparty_test_runner**: Contains the test runner for integration tests

These containers are orchestrated using Docker Compose, which defines the network, volumes, and environment variables for each container.

## Docker Resources

- **Project Name**: turdparty
- **Networks**:
  - turdparty_test_network: Network for test environment
  - turdparty_dev_network: Network for development environment
  - turdparty_prod_network: Network for production environment
- **Volumes**:
  - postgres_data: Persistent storage for PostgreSQL
  - minio_data: Persistent storage for MinIO
  - turdparty_ssh_keys: Storage for SSH keys

## Authentication Flow

1. User submits login credentials through the frontend
2. Frontend sends credentials to the backend API's authentication endpoint
3. Backend API validates credentials and issues a JWT token
4. Frontend stores the JWT token and includes it in subsequent requests
5. Backend API validates the JWT token for protected endpoints

## MFA (Multi-Factor Authentication)

The application supports MFA for enhanced security:

1. User enables MFA through the frontend
2. Backend generates a QR code for the user to scan with an authenticator app
3. User enters the code from the authenticator app to verify MFA setup
4. For subsequent logins, user must provide both password and MFA code

## File Storage

The application includes a file storage system for uploading and managing files:

1. User uploads files through the frontend
2. Frontend sends files to the backend API's file upload endpoint
3. Backend API stores files and metadata
4. Files can be retrieved, listed, and deleted through the API

## VM and Container Management

The application includes functionality for managing virtual machines and containers:

1. Users can create, start, stop, and delete VMs and containers
2. The backend API communicates with the host system to manage these resources
3. Status and logs can be viewed through the frontend

## Health Monitoring

The application includes health check endpoints for monitoring the status of various components:

1. API health check endpoint
2. Database connection health check
3. Storage service health check