#!/usr/bin/env python
"""
Fix database issues by directly executing SQL statements.
"""
import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_database():
    """Fix database issues by directly executing SQL statements."""
    try:
        # Import SQLAlchemy
        from sqlalchemy import create_engine, text
        
        # Get database URL from environment
        database_url = os.environ.get("DATABASE_URL")
        if not database_url:
            logger.error("DATABASE_URL environment variable is not set")
            sys.exit(1)
        
        logger.info(f"Connecting to database: {database_url}")
        
        # Create engine
        engine = create_engine(database_url)
        
        # Connect to database and execute SQL
        with engine.connect() as conn:
            # Drop problematic index if it exists
            logger.info("Dropping problematic index")
            conn.execute(text("DROP INDEX IF EXISTS ix_test_cases_id;"))
            
            # Create required tables
            logger.info("Creating device_info table")
            conn.execute(text("""
            CREATE TABLE IF NOT EXISTS device_info (
                id UUID PRIMARY KEY,
                device_type VARCHAR(255),
                device_name VARCHAR(255),
                os_name VARCHAR(255),
                os_version VARCHAR(255),
                browser_name VARCHAR(255),
                browser_version VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """))
            
            logger.info("Creating users table if it doesn't exist")
            conn.execute(text("""
            CREATE TABLE IF NOT EXISTS users (
                id VARCHAR(255) PRIMARY KEY,
                username VARCHAR(255) NOT NULL,
                email VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                full_name VARCHAR(255),
                is_active BOOLEAN DEFAULT TRUE,
                is_superuser BOOLEAN DEFAULT FALSE,
                hashed_password VARCHAR(255)
            );
            """))
            
            logger.info("Creating environments table if it doesn't exist")
            conn.execute(text("""
            CREATE TABLE IF NOT EXISTS environments (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP,
                deleted_at TIMESTAMP,
                created_by VARCHAR(255) REFERENCES users(id)
            );
            """))
            
            logger.info("Creating test_cases table if it doesn't exist")
            conn.execute(text("""
            CREATE TABLE IF NOT EXISTS test_cases (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP,
                created_by VARCHAR(255) REFERENCES users(id)
            );
            """))
            
            # Commit changes
            conn.commit()
            
            logger.info("Database fixes applied successfully")
            
        return True
    except Exception as e:
        logger.error(f"Error fixing database: {e}")
        return False

if __name__ == "__main__":
    success = fix_database()
    sys.exit(0 if success else 1) 